{"name": "nanan", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development npm run build:env && echo '🚀 开发环境配置完成，请在微信开发者工具中打开项目'", "test": "cross-env NODE_ENV=test npm run build:env && echo '🧪 测试环境配置完成，使用线上测试库'", "build": "cross-env NODE_ENV=production npm run build:env && echo '📦 生产环境配置完成'", "build:test": "cross-env NODE_ENV=test npm run build:env && echo '📦 测试环境配置完成'", "build:env": "node scripts/build-env.js", "generate:env": "node scripts/generate-env.js", "generate:env:dev": "node scripts/generate-env.js development", "generate:env:test": "node scripts/generate-env.js test", "generate:env:prod": "node scripts/generate-env.js production", "build:npm": "echo '正在构建npm包...' && npm run clean:npm && npm install --production=false", "compile:npm": "npm run build:npm", "clean:npm": "rimraf miniprogram_npm", "preview": "echo '请在微信开发者工具中使用预览功能'", "upload": "echo '请在微信开发者工具中使用上传功能'"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@vant/weapp": "^1.11.6", "axios": "^1.9.0", "dayjs": "^1.11.11", "miniprogram-api-promise": "^1.0.4", "mobx-miniprogram": "^6.12.3", "mobx-miniprogram-bindings": "^3.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "rimraf": "^5.0.10"}}