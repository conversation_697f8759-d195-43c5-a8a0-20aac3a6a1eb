const prisma = require('../utils/prisma');
const {hashPassword} = require('../utils/password');
const {success, error} = require('../utils/response');
const wechatService = require('../services/wechatService');
const bcrypt = require('bcryptjs');

/**
 * 获取用户列表
 * @route GET /api/users
 */
const getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(page);
    const pageSize = parseInt(size);
    const skip = (pageNum - 1) * pageSize;

    // 构建查询条件
    const where = {};

    if (search) {
      where.OR = [
        {name: {contains: search, mode: 'insensitive'}},
        {phone: {contains: search, mode: 'insensitive'}}
      ];
    }

    if (role) {
      where.role = role;
    }

    // 获取总数
    const total = await prisma.user.count({where});

    // 获取用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            orders: true,
            messages: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: pageSize
    });

    // 格式化返回数据
    const formattedUsers = users.map(user => ({
      ...user,
      orderCount: user._count.orders,
      messageCount: user._count.messages,
      status: 1 // 默认状态为激活，后续可以从数据库字段获取
    }));

    return success(res, {
      list: formattedUsers,
      total,
      page: pageNum,
      size: pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (err) {
    console.error('Get users error:', err);
    return error(res, 'Failed to get users', 500);
  }
};

/**
 * 获取指定用户
 * @route GET /api/users/:id
 */
const getUserById = async (req, res) => {
  try {
    const {id} = req.params;

    const user = await prisma.user.findUnique({
      where: {id},
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        avatar: true,
        role: true,
        gender: true,
        birthday: true,
        address: true,
        bio: true,
        preferences: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 处理返回数据
    const responseData = {
      ...user,
      preferences: user.preferences ? JSON.parse(user.preferences) : null
    };

    return success(res, responseData);
  } catch (err) {
    console.error('Get user error:', err);
    return error(res, 'Failed to get user', 500);
  }
};

/**
 * 创建用户
 * @route POST /api/users
 */
const createUser = async (req, res) => {
  try {
    const {name, phone, password, role} = req.body;

    if (!name || !phone || !password) {
      return error(res, 'Name, phone and password are required', 400);
    }

    // 检查手机号是否已存在
    const existingUser = await prisma.user.findUnique({
      where: {phone}
    });

    if (existingUser) {
      return error(res, 'Phone number already registered', 409);
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        phone,
        password: hashedPassword,
        role: role || 'user'
      }
    });

    return success(
      res,
      {
        id: user.id,
        name: user.name,
        phone: user.phone,
        role: user.role,
        createdAt: user.createdAt
      },
      'User created successfully',
      201
    );
  } catch (err) {
    console.error('Create user error:', err);
    return error(res, 'Failed to create user', 500);
  }
};

/**
 * 更新用户
 * @route PUT /api/users/:id
 */
const updateUser = async (req, res) => {
  try {
    const {id} = req.params;
    const {
      name,
      phone,
      email,
      password,
      avatar,
      role,
      gender,
      birthday,
      address,
      bio,
      preferences,
      isFirstTimeSetup
    } = req.body;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: {id}
    });

    if (!existingUser) {
      return error(res, 'User not found', 404);
    }

    // 如果更新手机号，检查是否已被使用
    if (phone && phone !== existingUser.phone) {
      const phoneExists = await prisma.user.findUnique({
        where: {phone}
      });

      if (phoneExists) {
        return error(res, 'Phone number already in use', 409);
      }
    }

    // 如果更新邮箱，检查是否已被使用
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: {email}
      });

      if (emailExists) {
        return error(res, 'Email already in use', 409);
      }
    }

    // 准备更新数据
    const updateData = {};

    // 基础字段
    if (name !== undefined) updateData.name = name;
    if (phone !== undefined) updateData.phone = phone;
    if (email !== undefined) updateData.email = email || null;
    if (avatar !== undefined) updateData.avatar = avatar || null;
    if (role !== undefined) updateData.role = role;

    // 扩展字段（可选）
    if (gender !== undefined)
      updateData.gender = gender && gender.trim() ? gender : null;
    if (birthday !== undefined) {
      updateData.birthday = birthday ? new Date(birthday) : null;
    }
    if (address !== undefined)
      updateData.address = address && address.trim() ? address : null;
    if (bio !== undefined) updateData.bio = bio && bio.trim() ? bio : null;
    if (preferences !== undefined) {
      updateData.preferences = preferences ? JSON.stringify(preferences) : null;
    }

    // 如果更新密码，需要哈希处理
    if (password) {
      updateData.password = await hashPassword(password);

      // 如果是首次设置密码，记录日志
      if (isFirstTimeSetup) {
        console.log(`🔐 用户 ${id} 首次设置密码`);
      }
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: {id},
      data: updateData,
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        avatar: true,
        role: true,
        gender: true,
        birthday: true,
        address: true,
        bio: true,
        preferences: true,
        updatedAt: true
      }
    });

    // 处理返回数据，确保所有字段都存在
    const responseData = {
      id: updatedUser.id,
      name: updatedUser.name,
      phone: updatedUser.phone,
      email: updatedUser.email || '',
      avatar: updatedUser.avatar,
      role: updatedUser.role,
      gender: updatedUser.gender || 'male',
      birthday: updatedUser.birthday,
      address: updatedUser.address || '',
      bio: updatedUser.bio || '',
      preferences: updatedUser.preferences
        ? JSON.parse(updatedUser.preferences)
        : null,
      updatedAt: updatedUser.updatedAt
    };

    return success(res, responseData, 'User updated successfully');
  } catch (err) {
    console.error('Update user error:', err);
    return error(res, 'Failed to update user', 500);
  }
};

/**
 * 删除用户
 * @route DELETE /api/users/:id
 */
const deleteUser = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: {id}
    });

    if (!existingUser) {
      return error(res, 'User not found', 404);
    }

    // 删除用户
    await prisma.user.delete({
      where: {id}
    });

    return success(res, null, 'User deleted successfully');
  } catch (err) {
    console.error('Delete user error:', err);
    return error(res, 'Failed to delete user', 500);
  }
};

/**
 * 获取家庭成员列表（当前用户的关联用户 + 自己）
 * @route GET /api/users/family
 */
const getFamilyMembers = async (req, res) => {
  try {
    const currentUserId = req.user.id;

    console.log(`👥 获取用户 ${currentUserId} 的家庭成员列表...`);

    // 1. 获取当前用户的关联用户
    const connections = await prisma.userConnection.findMany({
      where: {
        OR: [
          {senderId: currentUserId, status: 'accepted'},
          {receiverId: currentUserId, status: 'accepted'}
        ]
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true,
            phone: true,
            openid: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            avatar: true,
            phone: true,
            openid: true
          }
        }
      }
    });

    // 2. 提取关联用户信息
    const connectedUsers = connections.map(conn => {
      return conn.senderId === currentUserId ? conn.receiver : conn.sender;
    });

    // 3. 获取当前用户信息
    const currentUser = await prisma.user.findUnique({
      where: {id: currentUserId},
      select: {
        id: true,
        name: true,
        avatar: true,
        phone: true,
        openid: true
      }
    });

    // 4. 组合家庭成员列表（当前用户 + 关联用户）
    const familyMembers = [currentUser, ...connectedUsers];

    console.log(`✅ 找到 ${familyMembers.length} 个家庭成员:`);
    familyMembers.forEach(member => {
      console.log(`  ${member.name} (${member.id})`);
    });

    return success(res, familyMembers);
  } catch (err) {
    console.error('Get family members error:', err);
    return error(res, 'Failed to get family members', 500);
  }
};

/**
 * 获取用户统计信息
 * @route GET /api/users/statistics
 */
const getUserStatistics = async (req, res) => {
  try {
    // 获取用户总数
    const totalUsers = await prisma.user.count();

    // 按角色统计
    const roleStats = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });

    // 今日新增用户
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: today
        }
      }
    });

    // 本月新增用户
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const monthUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thisMonth
        }
      }
    });

    // 活跃用户（有订单的用户）
    const activeUsers = await prisma.user.count({
      where: {
        orders: {
          some: {}
        }
      }
    });

    const statistics = {
      total: totalUsers,
      today: todayUsers,
      month: monthUsers,
      active: activeUsers,
      roleDistribution: roleStats.reduce((acc, stat) => {
        acc[stat.role] = stat._count.id;
        return acc;
      }, {})
    };

    return success(res, statistics);
  } catch (err) {
    console.error('Get user statistics error:', err);
    return error(res, 'Failed to get user statistics', 500);
  }
};

/**
 * 重置用户密码
 * @route PUT /api/users/:id/password
 */
const resetUserPassword = async (req, res) => {
  try {
    const {id} = req.params;
    const {password} = req.body;

    if (!password || password.length < 6) {
      return error(res, 'Password must be at least 6 characters long', 400);
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: {id}
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(password);

    // 更新密码
    await prisma.user.update({
      where: {id},
      data: {password: hashedPassword}
    });

    return success(res, null, 'Password reset successfully');
  } catch (err) {
    console.error('Reset password error:', err);
    return error(res, 'Failed to reset password', 500);
  }
};

/**
 * 批量删除用户
 * @route DELETE /api/users/batch
 */
const batchDeleteUsers = async (req, res) => {
  try {
    const {ids} = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, 'User IDs are required', 400);
    }

    // 检查是否包含管理员用户
    const adminUsers = await prisma.user.findMany({
      where: {
        id: {in: ids},
        role: 'admin'
      }
    });

    if (adminUsers.length > 0) {
      return error(res, 'Cannot delete admin users', 403);
    }

    // 批量删除用户
    const result = await prisma.user.deleteMany({
      where: {
        id: {in: ids}
      }
    });

    return success(
      res,
      {deletedCount: result.count},
      'Users deleted successfully'
    );
  } catch (err) {
    console.error('Batch delete users error:', err);
    return error(res, 'Failed to delete users', 500);
  }
};

/**
 * 绑定手机号（可选功能）
 * @route POST /api/users/bind-phone
 */
const bindPhone = async (req, res) => {
  try {
    const {phoneCode} = req.body;
    const userId = req.user.id;

    if (!phoneCode) {
      return error(res, '手机号授权码不能为空', 400);
    }

    // 获取用户手机号
    let phone;
    try {
      phone = await wechatService.getPhoneNumber(phoneCode);
    } catch (phoneError) {
      console.error('获取手机号失败:', phoneError);
      return error(res, '获取手机号失败，请重试', 400);
    }

    // 检查手机号是否已被其他用户使用
    const existingUser = await prisma.user.findUnique({
      where: {phone}
    });

    if (existingUser && existingUser.id !== userId) {
      return error(res, '该手机号已被其他用户绑定', 400);
    }

    // 更新用户手机号
    const updatedUser = await prisma.user.update({
      where: {id: userId},
      data: {phone},
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        openid: true
      }
    });

    console.log(`用户 ${userId} 绑定手机号: ${phone}`);

    return success(res, updatedUser, '手机号绑定成功');
  } catch (err) {
    console.error('绑定手机号失败:', err);
    return error(res, '绑定手机号失败', 500);
  }
};

/**
 * 账户合并 - 将微信账户与手机号账户合并
 * @route POST /api/users/:id/merge-account
 */
const mergeAccount = async (req, res) => {
  try {
    const {id} = req.params; // 当前微信用户ID
    const {phone, password} = req.body; // 要合并的手机号账户信息

    if (!phone || !password) {
      return error(res, 'Phone and password are required', 400);
    }

    // 查找当前微信用户
    const wechatUser = await prisma.user.findUnique({
      where: {id}
    });

    if (!wechatUser || !wechatUser.openid) {
      return error(res, 'WeChat user not found', 404);
    }

    // 查找要合并的手机号用户
    const phoneUser = await prisma.user.findUnique({
      where: {phone}
    });

    if (!phoneUser) {
      return error(res, 'Phone user not found', 404);
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, phoneUser.password);
    if (!isPasswordValid) {
      return error(res, 'Invalid password', 401);
    }

    // 检查手机号用户是否已有openid（避免重复合并）
    if (phoneUser.openid) {
      return error(res, 'Phone account already linked to WeChat', 409);
    }

    // 开始事务合并账户
    const result = await prisma.$transaction(async prisma => {
      // 1. 将微信用户的openid转移到手机号用户
      const updatedUser = await prisma.user.update({
        where: {id: phoneUser.id},
        data: {
          openid: wechatUser.openid,
          // 如果微信用户有更新的信息，也可以合并
          avatar: wechatUser.avatar || phoneUser.avatar,
          lastLoginAt: new Date()
        }
      });

      // 2. 转移微信用户的相关数据到手机号用户
      // 转移订单
      await prisma.order.updateMany({
        where: {userId: wechatUser.id},
        data: {userId: phoneUser.id}
      });

      // 转移消息
      await prisma.message.updateMany({
        where: {userId: wechatUser.id},
        data: {userId: phoneUser.id}
      });

      // 转移通知
      await prisma.notification.updateMany({
        where: {userId: wechatUser.id},
        data: {userId: phoneUser.id}
      });

      // 3. 删除微信用户记录
      await prisma.user.delete({
        where: {id: wechatUser.id}
      });

      return updatedUser;
    });

    console.log(
      `🔗 账户合并成功: 微信用户 ${wechatUser.id} 合并到手机号用户 ${phoneUser.id}`
    );

    return success(
      res,
      {
        id: result.id,
        name: result.name,
        phone: result.phone,
        email: result.email,
        avatar: result.avatar,
        openid: result.openid
      },
      'Account merged successfully'
    );
  } catch (err) {
    console.error('Merge account error:', err);
    return error(res, 'Failed to merge account', 500);
  }
};

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  getFamilyMembers,
  getUserStatistics,
  resetUserPassword,
  batchDeleteUsers,
  bindPhone,
  mergeAccount
};
