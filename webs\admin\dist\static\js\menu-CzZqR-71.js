import{a4 as t}from"./index-CtHojCwd.js";const i={getDishes:e=>{if(!e||Object.keys(e).length===0)return t.get("/dishes");const s=new URLSearchParams(e).toString();return t.get(`/dishes?${s}`)},getDishDetail:e=>t.get(`/dishes/${e}`),createDish:e=>t.post("/dishes",e),updateDish:(e,s)=>t.put(`/dishes/${e}`,s),deleteDish:e=>t.delete(`/dishes/${e}`),getCategories:()=>t.get("/dishes/categories"),createCategory:e=>t.post("/dishes/categories",e),updateCategory:(e,s)=>t.put(`/dishes/categories/${e}`,s),deleteCategory:e=>t.delete(`/dishes/categories/${e}`),getHotDishes:e=>t.get("/dishes/hot",{params:e}),getDishStatistics:()=>t.get("/dishes/statistics"),getDishAnalytics:e=>t.get("/dishes/analytics",{params:e}),exportDishes:e=>t.get("/dishes/export",{params:e}),batchOperation:e=>t.post("/dishes/batch",e),getDishesByCategory:()=>t.get("/dishes/by-category")},g={getMenus:e=>t.get("/menus",{params:e}),getTodayMenu:e=>t.get("/menus/today",{params:{date:e}}),getHistoryMenus:e=>{if(!e||Object.keys(e).length===0)return t.get("/menus/history");const s=new URLSearchParams(e).toString();return t.get(`/menus/history?${s}`)},createMenu:e=>t.post("/menus",e),updateMenu:(e,s)=>t.put(`/menus/${e}`,s),deleteMenu:e=>t.delete(`/menus/${e}`),removeDishFromMenu:(e,s)=>t.delete(`/menus/today/dishes/${s}`,{params:{date:e}}),clearTodayMenu:e=>t.delete("/menus/today",{params:{date:e}}),getStatistics:()=>t.get("/menus/statistics")};i.getCategories=e=>{if(!e||Object.keys(e).length===0)return t.get("/dishes/categories");const s=new URLSearchParams(e).toString();return t.get(`/dishes/categories?${s}`)};i.createCategory=e=>t.post("/dishes/categories",e);i.updateCategory=(e,s)=>t.put(`/dishes/categories/${e}`,s);i.deleteCategory=e=>t.delete(`/dishes/categories/${e}`);export{i as d,g as m};
