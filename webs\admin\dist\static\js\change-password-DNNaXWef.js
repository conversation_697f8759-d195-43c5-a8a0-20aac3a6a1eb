var x=(g,u,n)=>new Promise((P,c)=>{var s=d=>{try{f(n.next(d))}catch(w){c(w)}},v=d=>{try{f(n.throw(d))}catch(w){c(w)}},f=d=>d.done?P(d.value):Promise.resolve(d.value).then(s,v);f((n=n.apply(g,u)).next())});import{_ as N,r as V,u as R,a as T,c as U,e as l,w as a,f as i,b as k,h as L,d as r,k as E,z,y as A,E as _}from"./index-CtHojCwd.js";const I={class:"change-password-page"},M={class:"form-container"},S={class:"security-tips"},j={__name:"change-password",setup(g){const u=V(),n=V(!1),P=R(),c=k(),s=T({currentPassword:"",newPassword:"",confirmPassword:""}),w={currentPassword:[{validator:(t,e,o)=>{e?o():o(new Error("请输入当前密码"))},trigger:"blur"}],newPassword:[{validator:(t,e,o)=>{e?e.length<6||e.length>20?o(new Error("密码长度在 6 到 20 个字符")):e===s.currentPassword?o(new Error("新密码不能与当前密码相同")):o():o(new Error("请输入新密码"))},trigger:"blur"}],confirmPassword:[{validator:(t,e,o)=>{e?e!==s.newPassword?o(new Error("两次输入的密码不一致")):o():o(new Error("请确认新密码"))},trigger:"blur"}]},C=()=>x(this,null,function*(){try{yield u.value.validate(),yield z.confirm("修改密码后需要重新登录，确定要继续吗？","确认修改",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),n.value=!0;try{const t=yield A.changePassword(s.currentPassword,s.newPassword);t.code===200?(_.success("密码修改成功！请重新登录"),P.logout(),setTimeout(()=>{c.push("/login")},1500)):_.error(t.message||"修改密码失败")}catch(t){console.error("修改密码API调用失败:",t),_.error("修改密码失败，请检查当前密码是否正确")}}catch(t){t!=="cancel"&&(console.error("修改密码失败:",t),_.error("修改密码失败，请检查当前密码是否正确"))}finally{n.value=!1}}),b=()=>{u.value.resetFields()};return(t,e)=>{const o=i("el-input"),m=i("el-form-item"),h=i("el-button"),B=i("el-form"),y=i("el-card"),F=i("el-alert");return L(),U("div",I,[l(y,null,{header:a(()=>e[3]||(e[3]=[r("div",{class:"card-header"},[r("h3",null,"修改密码"),r("p",null,"为了账户安全，建议定期更换密码")],-1)])),default:a(()=>[r("div",M,[l(B,{ref_key:"changePasswordFormRef",ref:u,model:s,rules:w,"label-width":"100px",size:"large",style:{"max-width":"500px"}},{default:a(()=>[l(m,{label:"当前密码",prop:"currentPassword"},{default:a(()=>[l(o,{modelValue:s.currentPassword,"onUpdate:modelValue":e[0]||(e[0]=p=>s.currentPassword=p),type:"password",placeholder:"请输入当前密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),l(m,{label:"新密码",prop:"newPassword"},{default:a(()=>[l(o,{modelValue:s.newPassword,"onUpdate:modelValue":e[1]||(e[1]=p=>s.newPassword=p),type:"password",placeholder:"请输入新密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"]),e[4]||(e[4]=r("div",{class:"password-tips"},[r("p",null,"密码要求："),r("ul",null,[r("li",null,"长度在 6-20 个字符之间"),r("li",null,"建议包含字母、数字和特殊字符"),r("li",null,"不能与当前密码相同")])],-1))]),_:1,__:[4]}),l(m,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[l(o,{modelValue:s.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=p=>s.confirmPassword=p),type:"password",placeholder:"请再次输入新密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),l(m,null,{default:a(()=>[l(h,{type:"primary",loading:n.value,onClick:C},{default:a(()=>e[5]||(e[5]=[E(" 修改密码 ")])),_:1,__:[5]},8,["loading"]),l(h,{onClick:b},{default:a(()=>e[6]||(e[6]=[E(" 重置 ")])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])])]),_:1}),l(y,{style:{"margin-top":"20px"}},{header:a(()=>e[7]||(e[7]=[r("h4",null,"安全提示",-1)])),default:a(()=>[r("div",S,[l(F,{title:"密码安全建议",type:"info",closable:!1,"show-icon":""},{default:a(()=>e[8]||(e[8]=[r("ul",null,[r("li",null,"定期更换密码，建议每3-6个月更换一次"),r("li",null,"不要使用过于简单的密码，如生日、姓名等"),r("li",null,"不要在多个平台使用相同密码"),r("li",null,"如发现账户异常，请立即修改密码")],-1)])),_:1,__:[8]})])]),_:1})])}}},G=N(j,[["__scopeId","data-v-c217b407"]]);export{G as default};
