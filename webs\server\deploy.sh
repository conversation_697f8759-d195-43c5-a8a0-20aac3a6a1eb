#!/bin/bash
# 自动部署脚本
exec > >(tee -a /www/deploy.log) 2>&1
set -e

echo "🚀 开始自动部署..."
echo "触发时间: $(date --date='0 days ago' '+%Y-%m-%d %H:%M:%S')"
echo "执行用户: $(whoami)"
echo "WebHook触发: GitHub代码推送"
echo "=================================="

# 检查是否需要重启服务器和重新构建前端
NEED_RESTART=false
NEED_ADMIN_BUILD=false
# 设置项目路径
PROJECT_PATH="/www/wwwroot/www.huanglun.asia/api"
SERVER_PATH="$PROJECT_PATH/webs/server"
ADMIN_PATH="$PROJECT_PATH/webs/admin"

# 进入项目目录
cd $PROJECT_PATH
# 获取更新前的commit hash
OLD_COMMIT=$(git rev-parse HEAD)

# 拉取最新代码
echo "📥 拉取最新代码..."
git reset --hard HEAD       # 撤销所有修改
git clean -fd               # 删除未追踪文件（如你本地新增的 .log、tmp）
git pull origin master --verbose

# 获取更新后的commit hash
NEW_COMMIT=$(git rev-parse HEAD)

echo "📋 代码变更信息:"
echo "   旧版本: $OLD_COMMIT"
echo "   新版本: $NEW_COMMIT"

# 检查是否有webs/server目录的变更
if [ "$OLD_COMMIT" != "$NEW_COMMIT" ]; then
    echo "🔍 检查服务器代码变更..."
    CHANGED_FILES=$(git diff --name-only $OLD_COMMIT $NEW_COMMIT)
    echo "📝 变更文件列表:"
    echo "$CHANGED_FILES"

    # 检查是否有webs/server相关的变更
    if echo "$CHANGED_FILES" | grep -q "^webs/server/"; then
        echo "✅ 检测到服务器代码变更，需要重启服务"
        NEED_RESTART=true
    fi

    # 检查是否有webs/admin相关的变更
    if echo "$CHANGED_FILES" | grep -q "^webs/admin/"; then
        echo "✅ 检测到后台管理系统代码变更，需要重新构建"
        NEED_ADMIN_BUILD=true
    fi

    # 检查是否有前端小程序代码变更
    if echo "$CHANGED_FILES" | grep -qE "^(pages/|components/|utils/|config/|app\.|project\.)"; then
        echo "ℹ️  检测到小程序前端代码变更（无需服务器操作）"
    fi

    if [ "$NEED_RESTART" = false ] && [ "$NEED_ADMIN_BUILD" = false ]; then
        echo "ℹ️  只有小程序前端代码变更，跳过服务器重启和后台构建"
    fi
else
    echo "ℹ️  代码无变更，跳过所有操作"
    NEED_RESTART=false
    NEED_ADMIN_BUILD=false
fi

# 只有在需要重启时才执行服务器相关操作
if [ "$NEED_RESTART" = true ]; then
    echo "🔧 执行服务器更新操作..."

    # 进入服务器目录
    cd $SERVER_PATH

    # 安装/更新依赖
    echo "📦 安装依赖..."
    rm -rf node_modules
    rm -f package-lock.json yarn.lock pnpm-lock.yaml

    # 安装所有依赖（包括devDependencies，确保完整性）
    npm install

    # 验证关键依赖是否安装成功
    echo "🔍 验证关键依赖..."
    npm list node-cron || echo "⚠️ node-cron 未安装"
    npm list prisma || echo "⚠️ prisma 未安装"
    npm list express || echo "⚠️ express 未安装"

# 进入后台管理系统 
# cd $ADMIN_PATH

# 安装/更新依赖
# echo "📦 安装后台依赖..."
# rm -rf node_modules
# rm -f package-lock.json yarn.lock pnpm-lock.yaml

# npm install 

    # 切换到MySQL schema
    echo "🔄 切换数据库配置..."
    if [ -f "prisma/schema.mysql.prisma" ]; then
        cp prisma/schema.mysql.prisma prisma/schema.prisma
        echo "✅ 已切换到MySQL schema"
    else
        echo "⚠️  schema.mysql.prisma 文件不存在，使用当前schema"
        echo "📁 当前prisma目录内容:"
        ls -la prisma/
    fi

    # 生成Prisma客户端
    echo "🔧 生成Prisma客户端..."
    npx prisma generate

    # 同步数据库结构
    echo "🗄️ 同步数据库..."
    npx prisma db push

    # 自动重启服务（WebHook模式）
    echo "🔄 自动重启服务..."

    # 重启测试环境
    if pm2 list | grep -q "nannan-api-test"; then
        echo "重启测试环境服务..."
        pm2 restart nannan-api-test
    else
        echo "⚠️  测试环境服务不存在，请在宝塔面板中创建"
    fi

    # 重启生产环境
    if pm2 list | grep -q "nannan-api"; then
        echo "重启生产环境服务..."
        pm2 restart nannan-api
    else
        echo "⚠️  生产环境服务不存在，请在宝塔面板中创建"
    fi

    echo "✅ 服务器更新完成！"
else
    echo "⏭️  跳过服务器更新操作"
fi

# 处理后台管理系统构建
if [ "$NEED_ADMIN_BUILD" = true ]; then
    echo "🎨 开始构建后台管理系统..."

    # 进入后台管理系统目录
    cd $ADMIN_PATH

    # 检查是否有package.json
    if [ ! -f "package.json" ]; then
        echo "❌ 后台管理系统package.json不存在，跳过构建"
    else
        # 安装依赖
        echo "📦 安装后台管理系统依赖..."
        rm -rf node_modules
        rm -f package-lock.json yarn.lock pnpm-lock.yaml

        # 使用pnpm安装依赖（如果可用），否则使用npm
        if command -v pnpm &> /dev/null; then
            echo "使用pnpm安装依赖..."
            pnpm install

            # 构建生产环境版本
            echo "🔨 构建生产环境版本..."
            pnpm build

            echo "✅ 后台管理系统构建完成！"
            echo "📁 构建文件位置: $ADMIN_PATH/dist"

        else
            echo "使用npm安装依赖..."
            npm install

            # 构建生产环境版本
            echo "🔨 构建生产环境版本..."
            npm run build

            echo "✅ 后台管理系统构建完成！"
            echo "📁 构建文件位置: $ADMIN_PATH/dist"
        fi

        # 检查构建结果
        if [ -d "dist" ]; then
            echo "✅ 构建成功，dist目录已生成"
            echo "📊 构建文件大小:"
            du -sh dist/
        else
            echo "❌ 构建失败，dist目录不存在"
        fi
    fi
else
    echo "⏭️  跳过后台管理系统构建"
fi

echo "✅ 代码部署完成！"
echo "=================================="
echo "📊 当前服务状态："
pm2 status

echo ""
echo "🌐 访问地址："
echo "  🏠 管理后台: https://www.huanglun.asia"
echo "  🔌 API生产环境: https://www.huanglun.asia/api/health"
echo "  🧪 API测试环境: https://www.huanglun.asia/api-test/health"
echo "  📊 服务器状态: pm2 status"

# 显示部署总结
echo ""
echo "📋 本次部署总结："
if [ "$NEED_RESTART" = true ]; then
    echo "  ✅ 服务器代码已更新并重启"
fi
if [ "$NEED_ADMIN_BUILD" = true ]; then
    echo "  ✅ 后台管理系统已重新构建"
fi
if [ "$NEED_RESTART" = false ] && [ "$NEED_ADMIN_BUILD" = false ]; then
    echo "  ℹ️  只有小程序前端代码变更，无需服务器操作"
fi

# 测试环境和测试服务器只有直接手动打包 