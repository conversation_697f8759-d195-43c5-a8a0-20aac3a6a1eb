<view class="container">
  <view class="mine-user-card">
    <view class="mine-user-avatar">
      <image
        wx:if="{{userInfo.avatar}}"
        src="{{userInfo.avatar}}"
        mode="aspectFill"
      />
      <van-icon wx:else name="user-o" size="48rpx" color="#6366F1" />
    </view>
    <view class="mine-user-info">
      <view class="mine-user-name">{{userInfo.name || '用户'}}</view>
      <view class="mine-user-phone" wx:if="{{formattedPhone}}"
        >📱 {{formattedPhone}}</view
      >
      <!-- 温馨提示：没有手机号时显示 -->
      <view class="mine-user-tip" wx:if="{{!formattedPhone}}"
        >💡 设置手机号码，启用账户登录功能</view
      >
      <view class="mine-user-role" wx:if="{{userInfo.role}}"
        >👤 {{roleText}}</view
      >
      <view class="mine-user-id">🆔 ID: {{userInfo.id || '未知'}}</view>
    </view>
    <view class="mine-user-badge" bindtap="goToUserProfile">
      <van-icon name="edit" size="24rpx" color="#6366F1" />
    </view>
  </view>

  <view class="mine-links-section">
    <button class="mine-link-btn message" bindtap="goToFamilyMessage">
      <van-icon name="chat-o" size="36rpx" />
      <text style="margin-left: 16rpx;">留言</text>
    </button>

    <button class="mine-link-btn connection" bindtap="goToUserConnection">
      <van-icon name="friends-o" size="36rpx" />
      <text style="margin-left: 16rpx;">用户关联</text>
    </button>

    <button class="mine-link-btn dish-manage" bindtap="goToMyDishes">
      <van-icon name="apps-o" size="36rpx" />
      <text style="margin-left: 16rpx;">我的菜品</text>
      <view class="badge" wx:if="{{myDishCount > 0}}">{{myDishCount}}</view>
    </button>

    <button class="mine-link-btn notice" bindtap="goToNotificationCenter">
      <view class="btn-content">
        <van-icon name="bell" size="36rpx" />
        <text class="btn-text">通知中心</text>

        <!-- 未读角标 -->
        <view
          wx:if="{{unreadNotificationCount > 0}}"
          class="notification-badge"
        >
          {{unreadNotificationCount > 99 ? '99+' : unreadNotificationCount}}
        </view>
      </view>
    </button>

    <button class="mine-link-btn order" bindtap="goToOrderList">
      <van-icon name="orders-o" size="36rpx" />
      <text style="margin-left: 16rpx;">我的订单</text>
    </button>
  </view>

  <view class="mine-action-card">
    <button class="mine-action-btn logout-btn" bindtap="onLogout">
      <van-icon name="sign-out" size="32rpx" />
      <text>退出登录</text>
    </button>
  </view>
</view>
