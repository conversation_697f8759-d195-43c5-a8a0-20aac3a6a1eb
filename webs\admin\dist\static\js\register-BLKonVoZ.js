var C=(E,z,i)=>new Promise((u,x)=>{var d=m=>{try{t(i.next(m))}catch(h){x(h)}},f=m=>{try{t(i.throw(m))}catch(h){x(h)}},t=m=>m.done?u(m.value):Promise.resolve(m.value).then(d,f);t((i=i.apply(E,z)).next())});import{_ as O,r as y,a as Z,q as v,s as D,c as j,d as p,e as s,w as a,f as c,b as G,h as U,k as g,t as V,v as J,x as R,l as K,m as Q,i as X,p as Y,y as S,E as w,z as B}from"./index-CtHojCwd.js";const ee={class:"register-container"},re={class:"register-form"},oe={class:"code-input-group"},te={class:"password-strength"},se={class:"strength-bar"},ae={class:"form-footer"},le={__name:"register",setup(E){const z=G(),i=y(),u=y(!1),x=y(!1),d=y(0);let f=null;const t=Z({username:"",phone:"",email:"",emailCode:"",password:"",confirmPassword:"",agreement:!1}),M={username:[{validator:(r,e,o)=>{if(!e){o(new Error("请输入用户名"));return}if(!/^[a-zA-Z0-9_]{3,20}$/.test(e)){o(new Error("用户名只能包含字母、数字和下划线，长度3-20位"));return}o()},trigger:"blur"}],phone:[{validator:(r,e,o)=>{if(!e){o(new Error("请输入手机号"));return}if(!/^1[3-9]\d{9}$/.test(e)){o(new Error("请输入正确的手机号"));return}o()},trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],emailCode:[{validator:(r,e,o)=>{if(!e){o(new Error("请输入邮箱验证码"));return}if(e.length!==6){o(new Error("验证码为6位数字"));return}o()},trigger:"blur"}],password:[{validator:(r,e,o)=>{if(!e){o(new Error("请输入密码"));return}if(e.length<6){o(new Error("密码长度不能少于6位"));return}if(e.length>20){o(new Error("密码长度不能超过20位"));return}o()},trigger:"blur"}],confirmPassword:[{validator:(r,e,o)=>{if(!e){o(new Error("请再次输入密码"));return}if(e!==t.password){o(new Error("两次输入的密码不一致"));return}o()},trigger:"blur"}],agreement:[{validator:(r,e,o)=>{if(!e){o(new Error("请阅读并同意用户协议和隐私政策"));return}o()},trigger:"change"}]},_=v(()=>{const r=t.password;if(!r)return 0;let e=0;return r.length>=6&&(e+=1),r.length>=8&&(e+=1),/[a-z]/.test(r)&&(e+=1),/[A-Z]/.test(r)&&(e+=1),/\d/.test(r)&&(e+=1),/[!@#$%^&*(),.?":{}|<>]/.test(r)&&(e+=1),Math.min(e,4)}),T=v(()=>{const r=_.value;return["","弱","一般","强","很强"][r]||""}),k=v(()=>{const r=_.value;return["","weak","fair","good","strong"][r]||""}),$=v(()=>`${_.value/4*100}%`),A=()=>C(this,null,function*(){try{yield i.value.validateField("email"),x.value=!0;const r=yield S.sendEmailCode(t.email.trim(),"register");r.code===200?(w.success("验证码已发送到您的邮箱"),q()):w.error(r.message||"发送失败，请重试")}catch(r){console.error("发送验证码失败:",r),w.error("发送失败，请重试")}finally{x.value=!1}}),q=()=>{d.value=60,f=setInterval(()=>{d.value--,d.value<=0&&(clearInterval(f),f=null)},1e3)},F=()=>C(this,null,function*(){try{yield i.value.validate(),u.value=!0,w.info("正在注册，请稍候...");const r=yield S.adminRegister({username:t.username.trim(),phone:t.phone.trim(),email:t.email.trim(),password:t.password,emailCode:t.emailCode.trim()});if(r.code===200||r.code===201){const e=t.username.trim();w.success("注册成功！正在跳转到登录页面..."),i.value.resetFields(),setTimeout(()=>{z.push({path:"/login",query:{username:e}})},1500)}else w.error(r.message||"注册失败，请检查信息后重试")}catch(r){console.error("注册失败:",r);let e="注册失败，请重试";if(r.response){const o=r.response.status,n=r.response.data;o===400?e=n.message||"请求参数错误":o===409?e="用户名、手机号或邮箱已被注册":o===403?e="邀请码无效或已过期":o>=500&&(e="服务器错误，请稍后再试")}w.error(e)}finally{u.value=!1}}),L=()=>{B({title:"用户协议",message:`    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">

      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统用户协议</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 总则</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 本协议是您与楠楠家厨管理系统之间关于使用本系统服务的法律协议。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 您通过注册、登录、使用本系统，即表示您已阅读、理解并同意接受本协议的全部条款。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 账户管理</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您应当使用真实、准确、完整的信息注册账户。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 您有义务维护账户信息的安全性，不得将账户借给他人使用。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 如发现账户被盗用，应立即通知系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 使用规范</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 您应当合法、正当地使用本系统，不得从事违法违规活动。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 不得恶意攻击系统、传播病毒或进行其他危害系统安全的行为。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 不得利用系统从事商业竞争或其他损害系统利益的活动。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 数据保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 我们承诺保护您的个人信息安全，不会未经授权向第三方披露。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 您上传的数据仅用于系统功能实现，我们不会用于其他商业目的。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 免责声明</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 因不可抗力、网络故障等原因导致的服务中断，我们不承担责任。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您因违反本协议导致的损失，由您自行承担。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 协议变更</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们有权根据需要修改本协议，修改后的协议将在系统内公布。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 如您不同意修改后的协议，可以停止使用本系统。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        本协议最终解释权归楠楠家厨管理系统所有。<br>
        最后更新日期：2025年7月31
      </p>
    </div>
  `,dangerouslyUseHTMLString:!0,confirmButtonText:"我已阅读",showCancelButton:!1,closeOnClickModal:!0,customStyle:{width:"90vw",maxWidth:"700px",minWidth:"320px"}})},W=()=>{B({title:"隐私政策",message:`
    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">
      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统隐私政策</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 信息收集</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 我们收集您主动提供的信息，包括但不限于用户名、邮箱、手机号等。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 我们会自动收集您使用系统时的操作日志，用于系统优化和安全监控。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 信息使用</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您的个人信息仅用于提供系统服务、身份验证和安全保护。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 我们不会将您的个人信息用于营销推广或其他商业目的。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 在法律要求的情况下，我们可能需要配合相关部门提供必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 信息保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 我们采用行业标准的安全措施保护您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 您的密码经过加密存储，我们无法获取您的明文密码。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 我们定期进行安全审计，确保数据安全。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 信息共享</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 除本政策明确说明外，我们不会与第三方共享您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 在获得您明确同意的情况下，我们可能与合作伙伴共享必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 用户权利</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 您有权查询、更正、删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您可以随时注销账户，我们将删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.3 如对隐私政策有疑问，可以联系系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 政策更新</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们可能会不定期更新本隐私政策。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 重大变更会通过系统通知或邮件方式告知您。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        如有隐私相关问题，请联系：<EMAIL><br>
        最后更新日期：2025年7月31日
      </p>
    </div>
  `,dangerouslyUseHTMLString:!0,confirmButtonText:"我已阅读",showCancelButton:!1,closeOnClickModal:!0,customStyle:{width:"90vw",maxWidth:"700px",minWidth:"320px"}})};return D(()=>{f&&clearInterval(f)}),(r,e)=>{const o=c("el-input"),n=c("el-form-item"),P=c("el-button"),b=c("el-link"),I=c("el-checkbox"),N=c("el-icon"),H=c("el-form");return U(),j("div",ee,[p("div",re,[e[13]||(e[13]=p("div",{class:"form-header"},[p("h2",null,"管理员注册"),p("p",null,"创建新的管理员账户，开始使用楠楠家厨管理系统")],-1)),s(H,{ref_key:"registerFormRef",ref:i,model:t,rules:M,"label-width":"80px",size:"large"},{default:a(()=>[s(n,{label:"用户名",prop:"username"},{default:a(()=>[s(o,{modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=l=>t.username=l),placeholder:"请输入用户名（3-20位字母数字下划线）","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),s(n,{label:"手机号",prop:"phone"},{default:a(()=>[s(o,{modelValue:t.phone,"onUpdate:modelValue":e[1]||(e[1]=l=>t.phone=l),placeholder:"请输入手机号","prefix-icon":"Phone",clearable:"",maxlength:"11"},null,8,["modelValue"])]),_:1}),s(n,{label:"邮箱",prop:"email"},{default:a(()=>[s(o,{modelValue:t.email,"onUpdate:modelValue":e[2]||(e[2]=l=>t.email=l),placeholder:"请输入邮箱地址","prefix-icon":"Message",clearable:""},null,8,["modelValue"])]),_:1}),s(n,{label:"验证码",prop:"emailCode"},{default:a(()=>[p("div",oe,[s(o,{modelValue:t.emailCode,"onUpdate:modelValue":e[3]||(e[3]=l=>t.emailCode=l),placeholder:"请输入邮箱验证码","prefix-icon":"Lock",clearable:"",maxlength:"6"},null,8,["modelValue"]),s(P,{disabled:d.value>0||!t.email,onClick:A,class:"code-btn",loading:x.value},{default:a(()=>[g(V(d.value>0?`${d.value}s`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),s(n,{label:"密码",prop:"password"},{default:a(()=>[s(o,{modelValue:t.password,"onUpdate:modelValue":e[4]||(e[4]=l=>t.password=l),type:"password",placeholder:"请输入密码（6-20位，建议包含字母数字）","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"]),p("div",te,[p("div",se,[p("div",{class:R(["strength-fill",k.value]),style:J({width:$.value})},null,6)]),p("span",{class:R(["strength-text",k.value])},V(T.value),3)])]),_:1}),s(n,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[s(o,{modelValue:t.confirmPassword,"onUpdate:modelValue":e[5]||(e[5]=l=>t.confirmPassword=l),type:"password",placeholder:"请再次输入密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),s(n,{prop:"agreement"},{default:a(()=>[s(I,{modelValue:t.agreement,"onUpdate:modelValue":e[6]||(e[6]=l=>t.agreement=l)},{default:a(()=>[e[10]||(e[10]=g(" 我已阅读并同意 ")),s(b,{type:"primary",onClick:L},{default:a(()=>e[8]||(e[8]=[g("《用户协议》")])),_:1,__:[8]}),e[11]||(e[11]=g(" 和 ")),s(b,{type:"primary",onClick:W},{default:a(()=>e[9]||(e[9]=[g("《隐私政策》")])),_:1,__:[9]})]),_:1,__:[10,11]},8,["modelValue"])]),_:1}),s(n,null,{default:a(()=>[s(P,{type:"primary",loading:u.value,onClick:F,disabled:!t.agreement,style:{width:"100%"}},{default:a(()=>[u.value?Q("",!0):(U(),K(N,{key:0},{default:a(()=>[s(X(Y))]),_:1})),g(" "+V(u.value?"注册中...":"立即注册"),1)]),_:1},8,["loading","disabled"])]),_:1}),p("div",ae,[s(b,{type:"primary",onClick:e[7]||(e[7]=l=>r.$router.push("/login"))},{default:a(()=>e[12]||(e[12]=[g(" 已有账户？立即登录 ")])),_:1,__:[12]})])]),_:1},8,["model"])])])}}},fe=O(le,[["__scopeId","data-v-58ffe41b"]]);export{fe as default};
