import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoadingStore = defineStore('loading', () => {
  // 全局加载状态
  const globalLoading = ref(false)
  const loadingText = ref('加载中...')
  const loadingTip = ref('')
  
  // 页面加载状态
  const pageLoading = ref(false)
  
  // 请求加载状态计数器
  const requestCount = ref(0)
  
  // 显示全局加载
  const showGlobalLoading = (text = '加载中...', tip = '') => {
    globalLoading.value = true
    loadingText.value = text
    loadingTip.value = tip
  }
  
  // 隐藏全局加载
  const hideGlobalLoading = () => {
    globalLoading.value = false
    loadingText.value = '加载中...'
    loadingTip.value = ''
  }
  
  // 显示页面加载
  const showPageLoading = () => {
    pageLoading.value = true
  }
  
  // 隐藏页面加载
  const hidePageLoading = () => {
    pageLoading.value = false
  }
  
  // 开始请求加载
  const startRequestLoading = () => {
    requestCount.value++
    if (requestCount.value === 1) {
      globalLoading.value = true
      loadingText.value = '请求处理中...'
    }
  }
  
  // 结束请求加载
  const endRequestLoading = () => {
    if (requestCount.value > 0) {
      requestCount.value--
    }
    if (requestCount.value === 0) {
      globalLoading.value = false
      loadingText.value = '加载中...'
      loadingTip.value = ''
    }
  }
  
  // 重置所有加载状态
  const resetLoading = () => {
    globalLoading.value = false
    pageLoading.value = false
    requestCount.value = 0
    loadingText.value = '加载中...'
    loadingTip.value = ''
  }
  
  return {
    // 状态
    globalLoading,
    loadingText,
    loadingTip,
    pageLoading,
    requestCount,
    
    // 方法
    showGlobalLoading,
    hideGlobalLoading,
    showPageLoading,
    hidePageLoading,
    startRequestLoading,
    endRequestLoading,
    resetLoading
  }
})

// 创建加载管理器
export const createLoadingManager = () => {
  const loadingStore = useLoadingStore()
  
  return {
    // 显示加载
    show: (text, tip) => loadingStore.showGlobalLoading(text, tip),
    
    // 隐藏加载
    hide: () => loadingStore.hideGlobalLoading(),
    
    // 异步操作包装器
    async wrap(asyncFn, text = '处理中...', tip = '') {
      try {
        loadingStore.showGlobalLoading(text, tip)
        return await asyncFn()
      } finally {
        loadingStore.hideGlobalLoading()
      }
    },
    
    // 页面加载包装器
    async wrapPage(asyncFn) {
      try {
        loadingStore.showPageLoading()
        return await asyncFn()
      } finally {
        loadingStore.hidePageLoading()
      }
    }
  }
}
