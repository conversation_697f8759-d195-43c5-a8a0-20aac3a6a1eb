import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useAppStore = defineStore('app', () => {
  // 状态
  const layout = ref('vertical'); // vertical, horizontal, mix
  const device = ref('desktop'); // desktop, mobile
  const sidebar = ref({
    opened: true,
    withoutAnimation: false
  });

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebar.value.opened = !sidebar.value.opened;
    sidebar.value.withoutAnimation = false;
  };

  // 关闭侧边栏
  const closeSidebar = (withoutAnimation = false) => {
    sidebar.value.opened = false;
    sidebar.value.withoutAnimation = withoutAnimation;
  };

  // 设置设备类型
  const setDevice = (deviceType) => {
    device.value = deviceType;
  };

  // 设置布局
  const setLayout = (layoutType) => {
    layout.value = layoutType;
  };

  return {
    layout,
    device,
    sidebar,
    toggleSidebar,
    closeSidebar,
    setDevice,
    setLayout
  };
});

// 导出hook函数以保持兼容性
export const useAppStoreHook = () => {
  return useAppStore();
};
