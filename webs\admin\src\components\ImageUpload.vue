<template>
  <div class="image-upload">
    <div
      class="upload-area"
      :class="{ 'drag-over': isDragOver }"
      @click="handleClick"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        :multiple="multiple"
        class="hidden"
        @change="handleFileSelect"
      />

      <!-- 上传区域 -->
      <div v-if="!imageList.length || multiple" class="upload-placeholder">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽上传图片</p>
          <p class="upload-tip">
            支持 JPG、PNG 格式，单个文件不超过 {{ formatFileSize(maxSize) }}
          </p>
        </div>
      </div>

      <!-- 图片预览列表 -->
      <div v-if="imageList.length" class="image-list">
        <div
          v-for="(image, index) in imageList"
          :key="index"
          class="image-item"
        >
          <el-image
            :src="image.url"
            :alt="image.name"
            fit="cover"
            class="preview-image"
            :preview-src-list="[image.url]"
          />
          <div class="image-overlay">
            <div class="image-actions">
              <el-button
                type="primary"
                :icon="View"
                circle
                size="small"
                @click.stop="previewImage(image)"
              />
              <el-button
                type="danger"
                :icon="Delete"
                circle
                size="small"
                @click.stop="removeImage(index)"
              />
            </div>
          </div>
          <div v-if="image.uploading" class="upload-progress">
            <el-progress :percentage="image.progress" :show-text="false" />
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-status">
      <el-progress :percentage="uploadProgress" :status="uploadStatus" />
      <p class="upload-message">{{ uploadMessage }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, View, Delete } from '@element-plus/icons-vue'
import * as uploadApi from '@/api/upload'
import { formatFileSize } from '@/utils/common'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB
  },
  maxCount: {
    type: Number,
    default: 9
  },
  uploadType: {
    type: String,
    default: 'general' // avatar, menu, general
  },
  entityId: {
    type: String,
    default: ''
  },
  category: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error'])

const fileInput = ref(null)
const imageList = ref([])
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadMessage = ref('')

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && newVal.length > 0) {
    imageList.value = newVal.map(item => ({
      url: typeof item === 'string' ? item : item.url,
      name: typeof item === 'string' ? '' : item.name || '',
      uploading: false,
      progress: 100
    }))
  } else {
    imageList.value = []
  }
}, { immediate: true })

// 点击上传
const handleClick = () => {
  if (props.disabled) return
  if (!props.multiple && imageList.value.length >= 1) return
  if (imageList.value.length >= props.maxCount) {
    ElMessage.warning(`最多只能上传 ${props.maxCount} 张图片`)
    return
  }
  fileInput.value?.click()
}

// 拖拽相关
const handleDragOver = () => {
  if (props.disabled) return
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (e) => {
  if (props.disabled) return
  isDragOver.value = false
  const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'))
  if (files.length > 0) {
    handleFiles(files)
  }
}

// 文件选择
const handleFileSelect = (e) => {
  const files = Array.from(e.target.files)
  handleFiles(files)
  // 清空input值，允许重复选择同一文件
  e.target.value = ''
}

// 处理文件
const handleFiles = (files) => {
  if (!props.multiple && imageList.value.length >= 1) {
    ElMessage.warning('只能上传一张图片')
    return
  }

  const remainingSlots = props.maxCount - imageList.value.length
  if (files.length > remainingSlots) {
    ElMessage.warning(`最多还能上传 ${remainingSlots} 张图片`)
    files = files.slice(0, remainingSlots)
  }

  files.forEach(file => {
    if (!validateFile(file)) return
    uploadFile(file)
  })
}

// 验证文件
const validateFile = (file) => {
  if (!file.type.startsWith('image/')) {
    ElMessage.error('只能上传图片文件')
    return false
  }

  if (file.size > props.maxSize) {
    ElMessage.error(`图片大小不能超过 ${formatFileSize(props.maxSize)}`)
    return false
  }

  return true
}

// 上传文件
const uploadFile = async (file) => {
  const imageItem = {
    url: URL.createObjectURL(file),
    name: file.name,
    uploading: true,
    progress: 0,
    file: file
  }

  imageList.value.push(imageItem)
  updateModelValue()

  try {
    uploading.value = true
    uploadMessage.value = '正在上传图片...'

    // 创建FormData
    const formData = new FormData()
    formData.append('image', file)
    formData.append('type', props.uploadType)
    formData.append('entityId', props.entityId)
    formData.append('category', props.category)

    // 调用上传API
    const response = await uploadApi.uploadImage(formData, {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        imageItem.progress = progress
        uploadProgress.value = progress
      }
    })

    // 上传成功
    imageItem.uploading = false
    imageItem.url = response.data.url
    imageItem.progress = 100

    uploading.value = false
    uploadMessage.value = '上传成功'

    updateModelValue()
    emit('upload-success', response.data)

    ElMessage.success('图片上传成功')

  } catch (error) {
    console.error('图片上传失败:', error)

    // 移除失败的图片
    const index = imageList.value.indexOf(imageItem)
    if (index > -1) {
      imageList.value.splice(index, 1)
    }

    uploading.value = false
    uploadMessage.value = '上传失败'

    updateModelValue()
    emit('upload-error', error)

    ElMessage.error(error.message || '图片上传失败')
  }
}

// 预览图片
const previewImage = () => {
  // Element Plus 的 el-image 组件会自动处理预览
}

// 移除图片
const removeImage = (index) => {
  imageList.value.splice(index, 1)
  updateModelValue()
}

// 更新模型值
const updateModelValue = () => {
  const urls = imageList.value
    .filter(item => !item.uploading && item.url)
    .map(item => item.url)
  emit('update:modelValue', urls)
}
</script>

<style scoped>
.image-upload {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-area:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-area.drag-over {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.hidden {
  display: none;
}

.upload-placeholder {
  text-align: center;
  padding: 20px;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 0;
  color: #606266;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  padding: 12px;
}

.image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px;
}

.upload-status {
  margin-top: 12px;
}

.upload-message {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #606266;
}
</style>
