var g=(t,a,n)=>new Promise((c,l)=>{var d=e=>{try{o(n.next(e))}catch(s){l(s)}},r=e=>{try{o(n.throw(e))}catch(s){l(s)}},o=e=>e.done?c(e.value):Promise.resolve(e.value).then(d,r);o((n=n.apply(t,a)).next())});import{C}from"./CustomTable-C1GDYDsI.js";import{o as b}from"./order-CyubyprT.js";import{_ as T,a6 as h,c as $,e as i,w as p,f as m,r as _,o as k,E as u,h as v,k as f,t as w}from"./index-CtHojCwd.js";const M=h({name:"TodayOrders",components:{CustomTable:C},setup(){const t=_(!1),a=_([]),n=[{prop:"id",label:"订单号",width:100},{prop:"userName",label:"用户"},{prop:"items",label:"菜品数量"},{prop:"status",label:"状态",slot:"status"},{prop:"mealTime",label:"用餐时间"}],c=()=>g(this,null,function*(){t.value=!0;try{const e=yield b.getTodayOrders();e.code===200?a.value=e.data||[]:u.error(e.message||"加载数据失败")}catch(e){console.error("加载今日订单失败:",e),u.error("加载数据失败")}finally{t.value=!1}}),l=e=>({pending:"warning",completed:"success",cancelled:"danger"})[e]||"info",d=e=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[e]||"未知",r=e=>{u.info(`查看订单: ${e.id}`)},o=e=>{u.success(`订单 ${e.id} 已完成`)};return k(()=>{c()}),{loading:t,tableData:a,columns:n,getStatusType:l,getStatusText:d,handleView:r,handleComplete:o}}}),S={class:"today-orders"};function D(t,a,n,c,l,d){const r=m("el-tag"),o=m("el-button"),e=m("CustomTable");return v(),$("div",S,[i(e,{title:"今日订单",data:t.tableData,columns:t.columns,loading:t.loading,"show-search":!1},{status:p(({row:s})=>[i(r,{type:t.getStatusType(s.status)},{default:p(()=>[f(w(t.getStatusText(s.status)),1)]),_:2},1032,["type"])]),actions:p(({row:s})=>[i(o,{size:"small",onClick:y=>t.handleView(s)},{default:p(()=>a[0]||(a[0]=[f("查看")])),_:2,__:[0]},1032,["onClick"]),i(o,{size:"small",type:"primary",onClick:y=>t.handleComplete(s)},{default:p(()=>a[1]||(a[1]=[f("完成")])),_:2,__:[1]},1032,["onClick"])]),_:1},8,["data","columns","loading"])])}const B=T(M,[["render",D]]);export{B as default};
