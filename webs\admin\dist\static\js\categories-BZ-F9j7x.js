var ce=Object.defineProperty;var H=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable;var P=(f,n,o)=>n in f?ce(f,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):f[n]=o,R=(f,n)=>{for(var o in n||(n={}))me.call(n,o)&&P(f,o,n[o]);if(H)for(var o of H(n))fe.call(n,o)&&P(f,o,n[o]);return f};var E=(f,n,o)=>new Promise((T,g)=>{var A=i=>{try{h(o.next(i))}catch(C){g(C)}},V=i=>{try{h(o.throw(i))}catch(C){g(C)}},h=i=>i.done?T(i.value):Promise.resolve(i.value).then(A,V);h((o=o.apply(f,n)).next())});import{_ as _e,r as v,a as q,q as ve,o as ge,c as F,e as t,w as l,f as m,E as d,h as $,k as s,i as b,I as he,Y as ye,X as be,t as k,H as Ce,a0 as we,a1 as L,d as U,m as W,N as xe,O as ke,l as Ve}from"./index-CtHojCwd.js";import{C as De}from"./CustomTable-C1GDYDsI.js";import{d as z}from"./menu-CzZqR-71.js";import{a as I}from"./common-DyWwJEEp.js";import{v as Ee,i as ze,d as Te,e as Ae}from"./excel-Db4e3gim.js";const Ne={class:"category-management"},Se={class:"dialog-footer"},$e={key:0,class:"category-detail"},Be={key:0,class:"category-dishes"},Fe={class:"dish-list"},Ie={__name:"categories",setup(f){const n=v(!1),o=v(!1),T=v([]),g=v(!1),A=v(!1),V=v(!1),h=v(),i=v(null),C=v([]),y=q({page:1,size:10,total:0}),B=q({}),u=v({name:"",description:"",sort:0}),X=[{prop:"name",label:"分类名称",minWidth:120},{prop:"description",label:"描述",minWidth:150,showOverflowTooltip:!0},{prop:"dishCount",label:"菜品数量",width:100,slot:!0},{prop:"sort",label:"排序",width:80},{prop:"createdAt",label:"创建时间",width:150,formatter:a=>I(a.createdAt,"YYYY-MM-DD HH:mm")},{label:"操作",width:180,slot:"operation",fixed:"right"}],G=[{prop:"name",label:"分类名称",type:"input",placeholder:"请输入分类名称"}],J={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:20,message:"分类名称长度在 2 到 20 个字符",trigger:"blur"}]},K=ve(()=>V.value?"编辑分类":"新增分类"),w=()=>E(this,null,function*(){n.value=!0;try{const a=R({page:y.page,size:y.size},B),e=yield z.getCategories(a);e.code===200?(T.value=e.data.list||e.data||[],y.total=e.data.total||e.data.length||0):d.error(e.message||"加载数据失败")}catch(a){console.error("加载分类列表失败:",a),d.error("加载数据失败")}finally{n.value=!1}}),Q=a=>{Object.assign(B,a),y.page=1,w()},Z=()=>{Object.keys(B).forEach(a=>{delete B[a]}),y.page=1,w()},ee=a=>{y.page=a,w()},te=a=>{y.size=a,y.page=1,w()},ae=()=>{V.value=!1,u.value={name:"",description:"",sort:0},g.value=!0},le=a=>E(this,null,function*(){i.value=a;try{const e=yield z.getDishes({categoryId:a.id});e.data&&(C.value=e.data.list||e.data)}catch(e){console.error("加载分类菜品失败:",e),C.value=[]}A.value=!0}),oe=a=>{V.value=!0,u.value=R({},a),g.value=!0},re=a=>E(this,null,function*(){try{yield z.deleteCategory(a.id),d.success("删除成功"),w()}catch(e){console.error("删除分类失败:",e),d.error("删除失败")}}),se=()=>{const a=document.createElement("input");a.type="file",a.accept=".xlsx,.xls",a.onchange=e=>E(this,null,function*(){const c=e.target.files[0];if(!c)return;const p=Ee(c);if(!p.valid){d.error(p.errors.join(", "));return}try{d.info("正在导入数据...");const _=yield ze(c,{columnMapping:{分类名称:"name",描述:"description",排序:"sort"},validator:(x,N)=>{const S=[];return x.name||S.push(`第${N+1}行：分类名称不能为空`),{valid:S.length===0,errors:S}}}),M=_.map(x=>z.createCategory({name:x.name,description:x.description||"",sort:parseInt(x.sort)||0}));yield Promise.all(M),d.success(`成功导入 ${_.length} 条数据`),w()}catch(_){console.error("导入失败:",_),_.type==="validation"?d.error("数据验证失败，请检查Excel格式"):d.error("导入失败: "+_.message)}}),a.click()},ne=()=>{try{const a=[{prop:"name",label:"分类名称",width:120},{prop:"description",label:"描述",width:200},{prop:"dishCount",label:"菜品数量",width:100,formatter:e=>`${e} 道菜`},{prop:"sort",label:"排序",width:80},{prop:"createdAt",label:"创建时间",width:150,formatter:e=>I(e)}];Ae(T.value,a,"分类数据",{sheetName:"分类列表"})}catch(a){console.error("导出失败:",a),d.error("导出失败: "+a.message)}},ie=()=>{try{Te([{prop:"name",label:"分类名称",example:"热菜"},{prop:"description",label:"描述",example:"各种热菜类别"},{prop:"sort",label:"排序",example:"1"}],"分类导入模板")}catch(a){console.error("下载模板失败:",a),d.error("下载模板失败: "+a.message)}},de=()=>E(this,null,function*(){if(h.value)try{yield h.value.validate(),o.value=!0,V.value?(yield z.updateCategory(u.value.id,u.value),d.success("更新成功")):(yield z.createCategory(u.value),d.success("创建成功")),g.value=!1,w()}catch(a){if(a.errors)return;console.error("提交失败:",a),d.error("操作失败")}finally{o.value=!1}}),Y=()=>{g.value=!1,u.value={name:"",description:"",sort:0},h.value&&h.value.resetFields()};return ge(()=>{w()}),(a,e)=>{const c=m("el-icon"),p=m("el-button"),_=m("el-tag"),M=m("el-popconfirm"),x=m("el-input"),N=m("el-form-item"),S=m("el-input-number"),ue=m("el-form"),O=m("el-dialog"),D=m("el-descriptions-item"),pe=m("el-descriptions");return $(),F("div",Ne,[t(De,{title:"菜品分类管理",data:T.value,columns:X,loading:n.value,pagination:y,"show-search":!0,"search-fields":G,onSearch:Q,onReset:Z,onCurrentChange:ee,onSizeChange:te},{toolbar:l(()=>[t(p,{type:"primary",onClick:ae},{default:l(()=>[t(c,null,{default:l(()=>[t(b(Ce))]),_:1}),e[5]||(e[5]=s(" 新增分类 "))]),_:1,__:[5]}),t(p,{type:"success",onClick:se},{default:l(()=>[t(c,null,{default:l(()=>[t(b(we))]),_:1}),e[6]||(e[6]=s(" 批量导入 "))]),_:1,__:[6]}),t(p,{type:"warning",onClick:ie},{default:l(()=>[t(c,null,{default:l(()=>[t(b(L))]),_:1}),e[7]||(e[7]=s(" 下载模板 "))]),_:1,__:[7]}),t(p,{type:"info",onClick:ne},{default:l(()=>[t(c,null,{default:l(()=>[t(b(L))]),_:1}),e[8]||(e[8]=s(" 导出数据 "))]),_:1,__:[8]})]),dishCount:l(({row:r})=>[t(_,{type:"info",size:"small"},{default:l(()=>[s(k(r.dishCount||0)+" 道菜 ",1)]),_:2},1024)]),operation:l(({row:r})=>[t(p,{size:"small",type:"primary",link:"",onClick:j=>le(r)},{default:l(()=>[t(c,null,{default:l(()=>[t(b(he))]),_:1}),e[9]||(e[9]=s(" 查看 "))]),_:2,__:[9]},1032,["onClick"]),t(p,{size:"small",type:"warning",link:"",onClick:j=>oe(r)},{default:l(()=>[t(c,null,{default:l(()=>[t(b(ye))]),_:1}),e[10]||(e[10]=s(" 编辑 "))]),_:2,__:[10]},1032,["onClick"]),t(M,{title:"确定要删除这个分类吗？删除后该分类下的菜品将被移动到默认分类。",onConfirm:j=>re(r)},{reference:l(()=>[t(p,{size:"small",type:"danger",link:""},{default:l(()=>[t(c,null,{default:l(()=>[t(b(be))]),_:1}),e[11]||(e[11]=s(" 删除 "))]),_:1,__:[11]})]),_:2},1032,["onConfirm"])]),_:1},8,["data","loading","pagination"]),t(O,{modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=r=>g.value=r),title:K.value,width:"600px","close-on-click-modal":!1,onClose:Y},{footer:l(()=>[U("div",Se,[t(p,{onClick:Y},{default:l(()=>e[12]||(e[12]=[s("取消")])),_:1,__:[12]}),t(p,{type:"primary",onClick:de,loading:o.value},{default:l(()=>e[13]||(e[13]=[s(" 确定 ")])),_:1,__:[13]},8,["loading"])])]),default:l(()=>[t(ue,{ref_key:"formRef",ref:h,model:u.value,rules:J,"label-width":"100px"},{default:l(()=>[t(N,{label:"分类名称",prop:"name"},{default:l(()=>[t(x,{modelValue:u.value.name,"onUpdate:modelValue":e[0]||(e[0]=r=>u.value.name=r),placeholder:"请输入分类名称",maxlength:"20","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(N,{label:"分类描述",prop:"description"},{default:l(()=>[t(x,{modelValue:u.value.description,"onUpdate:modelValue":e[1]||(e[1]=r=>u.value.description=r),type:"textarea",rows:3,placeholder:"请输入分类描述",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(N,{label:"排序",prop:"sort"},{default:l(()=>[t(S,{modelValue:u.value.sort,"onUpdate:modelValue":e[2]||(e[2]=r=>u.value.sort=r),min:0,max:999,placeholder:"排序值，数字越小越靠前"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(O,{modelValue:A.value,"onUpdate:modelValue":e[4]||(e[4]=r=>A.value=r),title:"分类详情",width:"600px"},{default:l(()=>[i.value?($(),F("div",$e,[t(pe,{column:2,border:""},{default:l(()=>[t(D,{label:"分类名称"},{default:l(()=>[s(k(i.value.name),1)]),_:1}),t(D,{label:"菜品数量"},{default:l(()=>[t(_,{type:"info"},{default:l(()=>[s(k(i.value.dishCount||0)+" 道菜",1)]),_:1})]),_:1}),t(D,{label:"排序值"},{default:l(()=>[s(k(i.value.sort||0),1)]),_:1}),t(D,{label:"创建时间"},{default:l(()=>[s(k(b(I)(i.value.createdAt)),1)]),_:1}),t(D,{label:"更新时间",span:"2"},{default:l(()=>[s(k(b(I)(i.value.updatedAt)),1)]),_:1}),t(D,{label:"分类描述",span:"2"},{default:l(()=>[s(k(i.value.description||"暂无描述"),1)]),_:1})]),_:1}),C.value.length?($(),F("div",Be,[e[14]||(e[14]=U("h4",null,"该分类下的菜品",-1)),U("div",Fe,[($(!0),F(xe,null,ke(C.value,r=>($(),Ve(_,{key:r.id,class:"dish-tag",type:"success"},{default:l(()=>[s(k(r.name),1)]),_:2},1024))),128))])])):W("",!0)])):W("",!0)]),_:1},8,["modelValue"])])}}},He=_e(Ie,[["__scopeId","data-v-b47341c4"]]);export{He as default};
