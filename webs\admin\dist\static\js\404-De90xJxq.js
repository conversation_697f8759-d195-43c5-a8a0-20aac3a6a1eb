import{_ as p,c as k,d as o,e as t,w as l,f as u,b as m,h as v,k as r,i as e,j as _,ae as g,D as b,C as q,p as x,a8 as C}from"./index-CtHojCwd.js";const w={class:"not-found-container"},B={class:"not-found-content"},N={class:"error-actions"},V={class:"quick-links"},y={class:"link-grid"},j={class:"not-found-illustration"},z={class:"illustration-circle"},D={__name:"404",setup(E){const d=m(),f=()=>{d.push("/")},c=()=>{d.go(-1)};return(H,s)=>{const n=u("el-icon"),i=u("el-button"),a=u("router-link");return v(),k("div",w,[o("div",B,[s[7]||(s[7]=o("div",{class:"error-code"},"404",-1)),s[8]||(s[8]=o("div",{class:"error-message"},"页面未找到",-1)),s[9]||(s[9]=o("div",{class:"error-description"},"抱歉，您访问的页面不存在或已被移除",-1)),o("div",N,[t(i,{type:"primary",onClick:f},{default:l(()=>[t(n,null,{default:l(()=>[t(e(_))]),_:1}),s[0]||(s[0]=r(" 返回首页 "))]),_:1,__:[0]}),t(i,{onClick:c},{default:l(()=>[t(n,null,{default:l(()=>[t(e(g))]),_:1}),s[1]||(s[1]=r(" 返回上页 "))]),_:1,__:[1]})]),o("div",V,[s[6]||(s[6]=o("h4",null,"快速导航",-1)),o("div",y,[t(a,{to:"/dashboard",class:"quick-link"},{default:l(()=>[t(n,null,{default:l(()=>[t(e(_))]),_:1}),s[2]||(s[2]=o("span",null,"仪表盘",-1))]),_:1,__:[2]}),t(a,{to:"/menu/dishes",class:"quick-link"},{default:l(()=>[t(n,null,{default:l(()=>[t(e(b))]),_:1}),s[3]||(s[3]=o("span",null,"菜品管理",-1))]),_:1,__:[3]}),t(a,{to:"/order/list",class:"quick-link"},{default:l(()=>[t(n,null,{default:l(()=>[t(e(q))]),_:1}),s[4]||(s[4]=o("span",null,"订单管理",-1))]),_:1,__:[4]}),t(a,{to:"/user/list",class:"quick-link"},{default:l(()=>[t(n,null,{default:l(()=>[t(e(x))]),_:1}),s[5]||(s[5]=o("span",null,"用户管理",-1))]),_:1,__:[5]})])])]),o("div",j,[o("div",z,[t(n,{size:"120",color:"#e6f7ff"},{default:l(()=>[t(e(C))]),_:1})])])])}}},R=p(D,[["__scopeId","data-v-e6e868d5"]]);export{R as default};
