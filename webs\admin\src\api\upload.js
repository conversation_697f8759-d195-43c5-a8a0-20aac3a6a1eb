import request from '@/utils/request'

/**
 * 上传图片
 * @param {FormData} formData - 包含图片文件和其他参数的FormData
 * @param {Object} config - 请求配置，包括进度回调
 * @returns {Promise}
 */
export const uploadImage = (formData, config = {}) => {
  return request({
    url: '/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

/**
 * 删除图片
 * @param {string} imageUrl - 图片URL
 * @returns {Promise}
 */
export const deleteImage = (imageUrl) => {
  return request({
    url: '/upload/image',
    method: 'delete',
    data: {
      imageUrl
    }
  })
}

/**
 * 批量上传图片
 * @param {FileList} files - 文件列表
 * @param {Object} options - 上传选项
 * @returns {Promise}
 */
export const batchUploadImages = async (files, options = {}) => {
  const {
    type = 'general',
    entityId = '',
    category = '',
    onProgress = () => {}
  } = options

  const uploadPromises = Array.from(files).map((file, index) => {
    const formData = new FormData()
    formData.append('image', file)
    formData.append('type', type)
    formData.append('entityId', entityId)
    formData.append('category', category)

    return uploadImage(formData, {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(index, progress)
      }
    })
  })

  return Promise.all(uploadPromises)
}

/**
 * 上传头像
 * @param {File} file - 头像文件
 * @param {string} userId - 用户ID
 * @returns {Promise}
 */
export const uploadAvatar = (file, userId) => {
  const formData = new FormData()
  formData.append('image', file)
  formData.append('type', 'avatar')
  formData.append('entityId', userId)
  formData.append('category', 'user')

  return uploadImage(formData)
}

/**
 * 上传菜品图片
 * @param {File} file - 菜品图片文件
 * @param {string} menuId - 菜单ID
 * @returns {Promise}
 */
export const uploadMenuImage = (file, menuId) => {
  const formData = new FormData()
  formData.append('image', file)
  formData.append('type', 'menu')
  formData.append('entityId', menuId)
  formData.append('category', 'food')

  return uploadImage(formData)
}

/**
 * 压缩图片
 * @param {File} file - 原始图片文件
 * @param {Object} options - 压缩选项
 * @returns {Promise<File>}
 */
export const compressImage = (file, options = {}) => {
  const {
    quality = 0.8,
    maxWidth = 800,
    maxHeight = 600
  } = options

  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      canvas.width = width
      canvas.height = height

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)

      // 转换为Blob
      canvas.toBlob((blob) => {
        // 创建新的File对象
        const compressedFile = new File([blob], file.name, {
          type: 'image/jpeg',
          lastModified: Date.now()
        })
        resolve(compressedFile)
      }, 'image/jpeg', quality)
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * 验证图片文件
 * @param {File} file - 图片文件
 * @param {Object} options - 验证选项
 * @returns {Object} 验证结果
 */
export const validateImageFile = (file, options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    minWidth = 0,
    minHeight = 0,
    maxWidth = Infinity,
    maxHeight = Infinity
  } = options

  const errors = []

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    errors.push(`不支持的文件类型: ${file.type}`)
  }

  // 检查文件大小
  if (file.size > maxSize) {
    errors.push(`文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(2)}MB > ${(maxSize / 1024 / 1024).toFixed(2)}MB`)
  }

  // 如果需要检查图片尺寸，返回Promise
  if (minWidth > 0 || minHeight > 0 || maxWidth < Infinity || maxHeight < Infinity) {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        if (img.width < minWidth) {
          errors.push(`图片宽度不能小于 ${minWidth}px`)
        }
        if (img.height < minHeight) {
          errors.push(`图片高度不能小于 ${minHeight}px`)
        }
        if (img.width > maxWidth) {
          errors.push(`图片宽度不能大于 ${maxWidth}px`)
        }
        if (img.height > maxHeight) {
          errors.push(`图片高度不能大于 ${maxHeight}px`)
        }

        resolve({
          valid: errors.length === 0,
          errors,
          width: img.width,
          height: img.height
        })
      }
      img.onerror = () => {
        errors.push('无法读取图片文件')
        resolve({
          valid: false,
          errors
        })
      }
      img.src = URL.createObjectURL(file)
    })
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

export default {
  uploadImage,
  deleteImage,
  batchUploadImages,
  uploadAvatar,
  uploadMenuImage,
  compressImage,
  validateImageFile
}
