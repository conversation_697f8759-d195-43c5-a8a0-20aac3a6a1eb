var ee=Object.defineProperty,te=Object.defineProperties;var ae=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var le=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable;var J=(n,r,o)=>r in n?ee(n,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[r]=o,F=(n,r)=>{for(var o in r||(r={}))le.call(r,o)&&J(n,o,r[o]);if(H)for(var o of H(r))se.call(r,o)&&J(n,o,r[o]);return n},K=(n,r)=>te(n,ae(r));var D=(n,r,o)=>new Promise((E,d)=>{var y=a=>{try{v(o.next(a))}catch($){d($)}},C=a=>{try{v(o.throw(a))}catch($){d($)}},v=a=>a.done?E(a.value):Promise.resolve(a.value).then(y,C);v((o=o.apply(n,r)).next())});import{_ as X,r as U,a as P,G as Q,l as W,w as t,S as oe,L as ne,f,e,m as G,i as w,d as x,k as h,t as S,E as _,h as L,o as ue,c as re,U as ie,a1 as de,z as ce}from"./index-CtHojCwd.js";import{C as me}from"./CustomTable-C1GDYDsI.js";import{u as N}from"./user-C9pqxfjH.js";import{a as j}from"./common-DyWwJEEp.js";const pe={class:"avatar-section"},fe={class:"avatar-info"},_e={class:"dialog-footer"},ge={__name:"UserDialog",props:{modelValue:{type:Boolean,default:!1},userId:{type:[String,Number],default:null},isEdit:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(n,{emit:r}){const o=n,E=r,d=U(!1),y=U(!1),C=U(!1),v=U(),a=P({id:null,name:"",phone:"",role:"user",status:1,avatar:"",createdAt:null,lastLoginAt:null,orderCount:0,totalAmount:0}),$={name:[{required:!0,message:"请输入用户姓名",trigger:"blur"},{min:2,max:20,message:"姓名长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],status:[{required:!0,message:"请选择用户状态",trigger:"change"}]};Q(()=>o.modelValue,i=>{d.value=i,i&&o.userId&&q()}),Q(d,i=>{E("update:modelValue",i),i||M()});const q=()=>D(this,null,function*(){if(o.userId){y.value=!0;try{const i=yield N.getUserDetail(o.userId);i.code===200?Object.assign(a,i.data):_.error(i.message||"获取用户信息失败")}catch(i){console.error("获取用户信息失败:",i),_.error("获取用户信息失败")}finally{y.value=!1}}}),k=()=>D(this,null,function*(){if(v.value)try{yield v.value.validate(),C.value=!0;const i=yield N.updateUser(a.id,{name:a.name,phone:a.phone,role:a.role,status:a.status});i.code===200?(_.success("用户信息更新成功"),E("success"),B()):_.error(i.message||"更新失败")}catch(i){console.error("更新用户信息失败:",i),_.error("更新失败")}finally{C.value=!1}}),B=()=>{d.value=!1},M=()=>{v.value&&v.value.resetFields(),Object.assign(a,{id:null,name:"",phone:"",role:"user",status:1,avatar:"",createdAt:null,lastLoginAt:null,orderCount:0,totalAmount:0})};return(i,c)=>{const A=f("el-input"),b=f("el-form-item"),V=f("el-col"),T=f("el-row"),z=f("el-option"),R=f("el-select"),O=f("el-avatar"),l=f("el-button"),s=f("el-form"),m=f("el-dialog"),p=ne("loading");return L(),W(m,{modelValue:d.value,"onUpdate:modelValue":c[4]||(c[4]=g=>d.value=g),title:n.isEdit?"编辑用户":"用户详情",width:"600px","close-on-click-modal":!1,onClose:B},{footer:t(()=>[x("div",_e,[e(l,{onClick:B},{default:t(()=>c[6]||(c[6]=[h("取消")])),_:1,__:[6]}),n.isEdit?(L(),W(l,{key:0,type:"primary",onClick:k,loading:C.value},{default:t(()=>c[7]||(c[7]=[h(" 保存 ")])),_:1,__:[7]},8,["loading"])):G("",!0)])]),default:t(()=>[oe((L(),W(s,{ref_key:"formRef",ref:v,model:a,rules:$,"label-width":"100px"},{default:t(()=>[e(T,{gutter:20},{default:t(()=>[e(V,{span:12},{default:t(()=>[e(b,{label:"用户姓名",prop:"name"},{default:t(()=>[e(A,{modelValue:a.name,"onUpdate:modelValue":c[0]||(c[0]=g=>a.name=g),placeholder:"请输入用户姓名",disabled:!n.isEdit},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(V,{span:12},{default:t(()=>[e(b,{label:"手机号",prop:"phone"},{default:t(()=>[e(A,{modelValue:a.phone,"onUpdate:modelValue":c[1]||(c[1]=g=>a.phone=g),placeholder:"请输入手机号",disabled:!n.isEdit},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(T,{gutter:20},{default:t(()=>[e(V,{span:12},{default:t(()=>[e(b,{label:"用户角色",prop:"role"},{default:t(()=>[e(R,{modelValue:a.role,"onUpdate:modelValue":c[2]||(c[2]=g=>a.role=g),placeholder:"请选择用户角色",disabled:!n.isEdit,style:{width:"100%"}},{default:t(()=>[e(z,{label:"普通用户",value:"user"}),e(z,{label:"管理员",value:"admin"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(V,{span:12},{default:t(()=>[e(b,{label:"用户状态",prop:"status"},{default:t(()=>[e(R,{modelValue:a.status,"onUpdate:modelValue":c[3]||(c[3]=g=>a.status=g),placeholder:"请选择用户状态",disabled:!n.isEdit,style:{width:"100%"}},{default:t(()=>[e(z,{label:"正常",value:1}),e(z,{label:"禁用",value:0})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(T,{gutter:20},{default:t(()=>[e(V,{span:12},{default:t(()=>[e(b,{label:"注册时间"},{default:t(()=>[e(A,{value:w(j)(a.createdAt),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(V,{span:12},{default:t(()=>[e(b,{label:"最后登录"},{default:t(()=>[e(A,{value:w(j)(a.lastLoginAt),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),n.isEdit?G("",!0):(L(),W(T,{key:0,gutter:20},{default:t(()=>[e(V,{span:12},{default:t(()=>[e(b,{label:"订单数量"},{default:t(()=>[e(A,{value:a.orderCount||0,disabled:""},null,8,["value"])]),_:1})]),_:1}),e(V,{span:12},{default:t(()=>[e(b,{label:"消费总额"},{default:t(()=>[e(A,{value:"¥"+(a.totalAmount||0),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})),e(b,{label:"用户头像"},{default:t(()=>[x("div",pe,[e(O,{src:a.avatar,size:80},{default:t(()=>[h(S(a.name?a.name.charAt(0):"U"),1)]),_:1},8,["src"]),x("div",fe,[x("p",null,S(a.avatar?"已设置头像":"未设置头像"),1),n.isEdit?(L(),W(l,{key:0,size:"small",type:"primary"},{default:t(()=>c[5]||(c[5]=[h(" 更换头像 ")])),_:1,__:[5]})):G("",!0)])])]),_:1})]),_:1},8,["model"])),[[p,y.value]])]),_:1},8,["modelValue","title"])}}},ve=X(ge,[["__scopeId","data-v-46a0f052"]]),be={class:"user-management"},he={class:"user-info"},ye={class:"user-details"},Ce={class:"user-name"},Ve={class:"user-phone"},Ue={__name:"index",setup(n){const r=U(!1),o=U([]),E=U(!1),d=P({page:1,size:10,total:0}),y=P({name:"",phone:"",status:""}),C=U(!1),v=U(null),a=U(!1),$=[{prop:"user",label:"用户信息",minWidth:200,slot:!0},{prop:"orderCount",label:"订单数量",minWidth:100,slot:!0},{prop:"lastLoginTime",label:"最后登录",minWidth:160,slot:!0},{prop:"registerTime",label:"注册时间",minWidth:160,formatter:l=>j(l.registerTime)},{prop:"status",label:"状态",minWidth:100,slot:!0},{label:"操作",minWidth:180,slot:"operation",fixed:"right"}],q=[{prop:"name",label:"用户姓名",type:"input"},{prop:"phone",label:"手机号",type:"input"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"正常",value:"1"},{label:"禁用",value:"0"}]}],k=()=>D(this,null,function*(){r.value=!0,E.value=!0;try{const l=F({page:d.page,size:d.size},y),s=yield N.getUsers(l);if(s.code===200){const m=(s.data.list||[]).map(p=>K(F({},p),{status:p.status==="active"?1:0,registerTime:p.createdAt,lastLoginTime:p.lastLoginAt||p.updatedAt}));o.value=m,d.total=s.data.total||0}else _.error(s.message||"加载数据失败")}catch(l){console.error("加载用户数据失败:",l),_.error("加载数据失败")}finally{r.value=!1,setTimeout(()=>{E.value=!1},100)}}),B=l=>{Object.assign(y,l),d.page=1,k()},M=()=>{Object.keys(y).forEach(l=>{y[l]=""}),d.page=1,k()},i=l=>{d.page=l,k()},c=l=>{d.size=l,d.page=1,k()},A=()=>{k(),_.success("数据已刷新")},b=()=>{_.info("导出功能开发中...")},V=l=>{v.value=l.id,a.value=!1,C.value=!0},T=l=>{v.value=l.id,a.value=!0,C.value=!0},z=()=>{k()},R=l=>D(this,null,function*(){if(!(E.value||!l.id))try{const s=l.status===1?"active":"inactive",m=yield N.updateUser(l.id,{status:s});m.code===200?_.success("用户状态更新成功"):(_.error(m.message||"状态更新失败"),l.status=l.status===1?0:1)}catch(s){console.error("更新用户状态失败:",s),_.error("状态更新失败"),l.status=l.status===1?0:1}}),O=l=>D(this,null,function*(){const s=l.status===1?"禁用":"启用";try{yield ce.confirm(`确定要${s}用户 ${l.name} 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const m=l.status===1?0:1,p=m===1?"active":"inactive",g=yield N.updateUser(l.id,{status:p});g.code===200?(l.status=m,_.success(`用户${s}成功`)):_.error(g.message||`用户${s}失败`)}catch(m){m!=="cancel"&&(console.error(`用户${s}失败:`,m),_.error(`用户${s}失败`))}});return ue(()=>{k()}),(l,s)=>{const m=f("el-icon"),p=f("el-button"),g=f("el-avatar"),Y=f("el-switch"),Z=f("el-tag");return L(),re("div",be,[e(me,{title:"用户管理",data:o.value,columns:$,loading:r.value,pagination:d,"show-search":!0,"search-fields":q,onSearch:B,onReset:M,onCurrentChange:i,onSizeChange:c},{actions:t(()=>[e(p,{onClick:A},{default:t(()=>[e(m,null,{default:t(()=>[e(w(ie))]),_:1}),s[1]||(s[1]=h(" 刷新数据 "))]),_:1,__:[1]}),e(p,{onClick:b},{default:t(()=>[e(m,null,{default:t(()=>[e(w(de))]),_:1}),s[2]||(s[2]=h(" 导出用户 "))]),_:1,__:[2]})]),user:t(({row:u})=>[x("div",he,[e(g,{src:u.avatar,size:40},{default:t(()=>[h(S(u.name?u.name.charAt(0):"U"),1)]),_:2},1032,["src"]),x("div",ye,[x("div",Ce,S(u.name||"未知用户"),1),x("div",Ve,S(u.phone||"未知手机号"),1)])])]),status:t(({row:u})=>[e(Y,{modelValue:u.status,"onUpdate:modelValue":I=>u.status=I,"active-value":1,"inactive-value":0,"active-text":"正常","inactive-text":"禁用",onChange:I=>R(u)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),orderCount:t(({row:u})=>[e(Z,{type:"primary"},{default:t(()=>[h(S(u.orderCount)+"单",1)]),_:2},1024)]),lastLoginTime:t(({row:u})=>[x("span",null,S(w(j)(u.lastLoginTime)),1)]),operation:t(({row:u})=>[e(p,{size:"small",onClick:I=>V(u)},{default:t(()=>s[3]||(s[3]=[h("查看")])),_:2,__:[3]},1032,["onClick"]),e(p,{size:"small",type:"primary",onClick:I=>T(u)},{default:t(()=>s[4]||(s[4]=[h("编辑")])),_:2,__:[4]},1032,["onClick"]),e(p,{size:"small",type:u.status===1?"warning":"success",onClick:I=>O(u)},{default:t(()=>[h(S(u.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1},8,["data","loading","pagination"]),e(ve,{modelValue:C.value,"onUpdate:modelValue":s[0]||(s[0]=u=>C.value=u),"user-id":v.value,"is-edit":a.value,onSuccess:z},null,8,["modelValue","user-id","is-edit"])])}}},$e=X(Ue,[["__scopeId","data-v-c075b54b"]]);export{$e as default};
