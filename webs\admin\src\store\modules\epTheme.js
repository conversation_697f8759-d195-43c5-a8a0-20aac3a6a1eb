import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useEpThemeStore = defineStore('epTheme', () => {
  // 状态
  const epThemeColor = ref('#409EFF');
  const epTheme = ref('default');

  // 设置主题色
  const setEpThemeColor = (color) => {
    epThemeColor.value = color;
  };

  // 设置主题
  const setEpTheme = (theme) => {
    epTheme.value = theme;
  };

  return {
    epThemeColor,
    epTheme,
    setEpThemeColor,
    setEpTheme
  };
});

// 导出hook函数以保持兼容性
export const useEpThemeStoreHook = () => {
  return useEpThemeStore();
};
