var re=Object.defineProperty;var D=Object.getOwnPropertySymbols;var ie=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var E=(c,o,l)=>o in c?re(c,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):c[o]=l,F=(c,o)=>{for(var l in o||(o={}))ie.call(o,l)&&E(c,l,o[l]);if(D)for(var l of D(o))ce.call(o,l)&&E(c,l,o[l]);return c};var z=(c,o,l)=>new Promise((v,C)=>{var h=r=>{try{i(l.next(r))}catch(p){C(p)}},V=r=>{try{i(l.throw(r))}catch(p){C(p)}},i=r=>r.done?v(r.value):Promise.resolve(r.value).then(h,V);i((l=l.apply(c,o)).next())});import{_ as ue,r as x,a as R,o as pe,c as M,e as n,w as a,f as m,E as g,h as f,l as $,m as T,k as _,d as u,t as y,i as S,N as de,O as me,U as ge,a1 as _e,z as fe}from"./index-CtHojCwd.js";import{C as ye}from"./CustomTable-C1GDYDsI.js";import{m as ve}from"./message-BUBVAcIa.js";import{a as O}from"./common-DyWwJEEp.js";const he={class:"family-messages"},be={class:"user-info"},ke={class:"ml-2"},Ce={class:"message-content"},xe={class:"content-text"},Me={key:0,class:"message-images"},Ve={key:0,class:"more-images"},we={key:0,class:"reply-content"},ze={class:"reply-text"},Re={class:"reply-time"},Te={key:1,class:"no-reply"},Se={class:"reply-dialog"},Be={class:"original-message"},Ne={__name:"family",setup(c){const o=x(!1),l=x([]),v=x(!1),C=x(!1),h=x(null),V=x(),i=R({page:1,size:10,total:0}),r=R({status:"",userName:"",dateRange:[]}),p=R({content:""}),U=[{prop:"user",label:"用户",width:150,slot:!0},{prop:"content",label:"留言内容",minWidth:300,slot:!0},{prop:"status",label:"状态",width:100,slot:!0},{prop:"reply",label:"回复",minWidth:200,slot:!0},{prop:"createdAt",label:"留言时间",width:160,formatter:t=>O(t.createdAt)},{label:"操作",width:180,slot:"operation",fixed:"right"}],j=[{prop:"userName",label:"用户姓名",type:"input"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"待回复",value:"pending"},{label:"已回复",value:"replied"}]},{prop:"dateRange",label:"留言时间",type:"daterange"}],L={content:[{required:!0,message:"请输入回复内容",trigger:"blur"},{min:5,max:500,message:"回复内容长度在 5 到 500 个字符",trigger:"blur"}]},b=()=>z(this,null,function*(){o.value=!0;try{const t=F({page:i.page,size:i.size},r),e=yield ve.getMessages(t);e.code===200?(l.value=e.data.list||[],i.total=e.data.total||0):g.error(e.message||"加载数据失败")}catch(t){console.error("加载留言数据失败:",t),g.error("加载数据失败")}finally{o.value=!1}}),P=t=>({pending:"warning",replied:"success"})[t]||"info",W=t=>({pending:"待回复",replied:"已回复"})[t]||"未知",q=t=>{Object.assign(r,t),i.page=1,b()},I=()=>{Object.keys(r).forEach(t=>{Array.isArray(r[t])?r[t]=[]:r[t]=""}),i.page=1,b()},G=t=>{i.page=t,b()},H=t=>{i.size=t,i.page=1,b()},J=()=>{b(),g.success("数据已刷新")},K=()=>{g.info("导出功能开发中...")},Q=t=>{g.info(`查看留言：${t.content.substring(0,20)}...`)},X=t=>{h.value=t,p.content="",v.value=!0},Y=()=>z(this,null,function*(){if(V.value)try{yield V.value.validate(),C.value=!0,yield new Promise(t=>setTimeout(t,1e3)),h.value.status="replied",h.value.reply=p.content,h.value.replyTime=new Date,g.success("回复发送成功"),v.value=!1}catch(t){console.error("发送回复失败:",t),g.error("发送回复失败")}finally{C.value=!1}}),Z=t=>z(this,null,function*(){try{yield fe.confirm("确定要删除这条留言吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.success("留言删除成功"),b()}catch(e){e!=="cancel"&&g.error("删除留言失败")}});return pe(()=>{b()}),(t,e)=>{const B=m("el-icon"),k=m("el-button"),ee=m("el-avatar"),te=m("el-image"),se=m("el-tag"),ae=m("el-input"),le=m("el-form-item"),ne=m("el-form"),oe=m("el-dialog");return f(),M("div",he,[n(ye,{title:"家庭留言管理",data:l.value,columns:U,loading:o.value,pagination:i,"show-search":!0,"search-fields":j,onSearch:q,onReset:I,onCurrentChange:G,onSizeChange:H},{actions:a(()=>[n(k,{onClick:J},{default:a(()=>[n(B,null,{default:a(()=>[n(S(ge))]),_:1}),e[3]||(e[3]=_(" 刷新数据 "))]),_:1,__:[3]}),n(k,{onClick:K},{default:a(()=>[n(B,null,{default:a(()=>[n(S(_e))]),_:1}),e[4]||(e[4]=_(" 导出留言 "))]),_:1,__:[4]})]),user:a(({row:s})=>{var d,w;return[u("div",be,[n(ee,{src:(d=s.user)==null?void 0:d.avatar,size:32},{default:a(()=>{var N,A;return[_(y((A=(N=s.user)==null?void 0:N.name)==null?void 0:A.charAt(0)),1)]}),_:2},1032,["src"]),u("span",ke,y((w=s.user)==null?void 0:w.name),1)])]}),content:a(({row:s})=>[u("div",Ce,[u("p",xe,y(s.content),1),s.images&&s.images.length?(f(),M("div",Me,[(f(!0),M(de,null,me(s.images.slice(0,3),(d,w)=>(f(),$(te,{key:w,src:d,"preview-src-list":s.images,class:"message-image",fit:"cover"},null,8,["src","preview-src-list"]))),128)),s.images.length>3?(f(),M("span",Ve," +"+y(s.images.length-3),1)):T("",!0)])):T("",!0)])]),status:a(({row:s})=>[n(se,{type:P(s.status)},{default:a(()=>[_(y(W(s.status)),1)]),_:2},1032,["type"])]),reply:a(({row:s})=>[s.reply?(f(),M("div",we,[u("p",ze,y(s.reply),1),u("p",Re,y(S(O)(s.replyTime)),1)])):(f(),M("span",Te,"未回复"))]),operation:a(({row:s})=>[n(k,{size:"small",onClick:d=>Q(s)},{default:a(()=>e[5]||(e[5]=[_("查看")])),_:2,__:[5]},1032,["onClick"]),s.status==="pending"?(f(),$(k,{key:0,size:"small",type:"primary",onClick:d=>X(s)},{default:a(()=>e[6]||(e[6]=[_(" 回复 ")])),_:2,__:[6]},1032,["onClick"])):T("",!0),n(k,{size:"small",type:"danger",onClick:d=>Z(s)},{default:a(()=>e[7]||(e[7]=[_("删除")])),_:2,__:[7]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),n(oe,{modelValue:v.value,"onUpdate:modelValue":e[2]||(e[2]=s=>v.value=s),title:"回复留言",width:"600px","close-on-click-modal":!1},{footer:a(()=>[n(k,{onClick:e[1]||(e[1]=s=>v.value=!1)},{default:a(()=>e[9]||(e[9]=[_("取消")])),_:1,__:[9]}),n(k,{type:"primary",loading:C.value,onClick:Y},{default:a(()=>e[10]||(e[10]=[_(" 发送回复 ")])),_:1,__:[10]},8,["loading"])]),default:a(()=>{var s;return[u("div",Se,[u("div",Be,[e[8]||(e[8]=u("h4",null,"原留言内容：",-1)),u("p",null,y((s=h.value)==null?void 0:s.content),1)]),n(ne,{ref_key:"replyFormRef",ref:V,model:p,rules:L,"label-width":"80px"},{default:a(()=>[n(le,{label:"回复内容",prop:"content"},{default:a(()=>[n(ae,{modelValue:p.content,"onUpdate:modelValue":e[0]||(e[0]=d=>p.content=d),type:"textarea",rows:4,placeholder:"请输入回复内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]}),_:1},8,["modelValue"])])}}},Oe=ue(Ne,[["__scopeId","data-v-648d51c0"]]);export{Oe as default};
