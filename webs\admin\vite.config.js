import {defineConfig, loadEnv} from 'vite';
import vue from '@vitejs/plugin-vue';
import {resolve} from 'path';

// https://vitejs.dev/config/
export default defineConfig(({command, mode}) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(process.cwd(), 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 使用现代Sass API
          api: 'modern-compiler',
          // 静默弃用警告
          silenceDeprecations: ['legacy-js-api', 'import'],
          // 全局样式变量（如果需要的话）
          additionalData: `
          // 这里可以添加全局SCSS变量
        `
        }
      }
    },
    server: {
      port: parseInt(env.VITE_PORT) || 5173,
      host: '0.0.0.0',
      open: true,
      proxy: {
        '/api': {
          target: getProxyTarget(mode, env),
          changeOrigin: true,
          secure: false
        }
      }
    },
    build: {
      target: 'es2015',
      sourcemap: mode !== 'production',
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      }
    },

    // 环境变量配置
    define: {
      __APP_VERSION__: JSON.stringify(env.VITE_APP_VERSION || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    }
  };
});

// 获取代理目标地址
function getProxyTarget(mode, env) {
  if (mode === 'development') {
    return (
      env.VITE_API_BASE_URL?.replace('/api', '') || 'http://localhost:3000'
    );
  } else if (mode === 'test') {
    return (
      env.VITE_API_BASE_URL?.replace('/api', '') || 'https://www.huanglun.asia'
    );
  } else {
    return (
      env.VITE_API_BASE_URL?.replace('/api', '') || 'https://www.huanglun.asia'
    );
  }
}
