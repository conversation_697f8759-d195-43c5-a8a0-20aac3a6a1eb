var g=(M,z,k)=>new Promise(($,u)=>{var b=_=>{try{s(k.next(_))}catch(V){u(V)}},p=_=>{try{s(k.throw(_))}catch(V){u(V)}},s=_=>_.done?$(_.value):Promise.resolve(_.value).then(b,p);s((k=k.apply(M,z)).next())});import{_ as me,r as D,a as N,o as _e,c as U,d as o,m as j,e as a,w as n,f as r,S as fe,L as ge,l as C,E as c,h as v,i as R,a8 as H,k as i,t as d,N as ve,O as he,z as w}from"./index-CtHojCwd.js";import{b as ye}from"./common-DyWwJEEp.js";const ke={class:"user-connections-container"},be={class:"search-section"},Ce={class:"connections-list"},we={class:"user-info"},ze={class:"user-details"},Ve={class:"user-name"},xe={class:"user-phone"},Ie={class:"user-info"},Se={class:"user-details"},Be={class:"user-name"},De={class:"user-phone"},$e={class:"message-text"},Ee={key:1,class:"text-gray-400"},Ue={class:"pagination-container"},je={key:0,class:"batch-actions"},Ae={__name:"connections",setup(M){const z=D(!1),k=D([]),$=D([]),u=D([]),b=N({keyword:"",status:""}),p=N({page:1,size:20,total:0}),s=N({visible:!1,form:{id:"",remark:"",groupId:""}}),_=D(),V={remark:[{max:100,message:"备注不能超过100个字符",trigger:"blur"}]},m=()=>g(this,null,function*(){z.value=!0;try{k.value=[{id:"1",senderId:"user1",receiverId:"user2",status:"pending",message:"希望能够关联，方便查看菜单",remark:"家人",groupId:"group1",sender:{id:"user1",name:"张三",phone:"13800138001",avatar:""},receiver:{id:"user2",name:"李四",phone:"13800138002",avatar:""},group:{id:"group1",name:"家庭成员",color:"#409EFF"},createdAt:new Date}],p.total=1}catch(l){c.error("加载关联数据失败")}finally{z.value=!1}}),J=()=>g(this,null,function*(){try{$.value=[{id:"group1",name:"家庭成员"},{id:"group2",name:"朋友"},{id:"group3",name:"同事"}]}catch(l){console.error("加载分组数据失败:",l)}}),A=()=>{p.page=1,m()},K=l=>{u.value=l},Q=l=>({pending:"warning",accepted:"success",rejected:"danger"})[l]||"info",W=l=>({pending:"待确认",accepted:"已接受",rejected:"已拒绝"})[l]||"未知",X=l=>g(this,null,function*(){try{yield w.confirm("确定要通过这个关联申请吗？","确认操作"),c.success("关联申请已通过"),m()}catch(t){t!=="cancel"&&c.error("操作失败")}}),Y=l=>g(this,null,function*(){try{yield w.confirm("确定要拒绝这个关联申请吗？","确认操作"),c.success("关联申请已拒绝"),m()}catch(t){t!=="cancel"&&c.error("操作失败")}}),Z=l=>g(this,null,function*(){try{yield w.confirm("确定要解除这个用户关联吗？","确认操作",{type:"warning"}),c.success("用户关联已解除"),m()}catch(t){t!=="cancel"&&c.error("操作失败")}}),ee=l=>{s.form.id=l.id,s.form.remark=l.remark||"",s.form.groupId=l.groupId||"",s.visible=!0},te=()=>g(this,null,function*(){try{yield _.value.validate(),c.success("关联信息已更新"),s.visible=!1,m()}catch(l){console.error("保存失败:",l)}}),ae=()=>g(this,null,function*(){try{yield w.confirm(`确定要批量通过 ${u.value.length} 个关联申请吗？`,"确认操作"),c.success("批量操作成功"),m()}catch(l){l!=="cancel"&&c.error("批量操作失败")}}),ne=()=>g(this,null,function*(){try{yield w.confirm(`确定要批量拒绝 ${u.value.length} 个关联申请吗？`,"确认操作"),c.success("批量操作成功"),m()}catch(l){l!=="cancel"&&c.error("批量操作失败")}}),le=()=>g(this,null,function*(){try{yield w.confirm(`确定要批量解除 ${u.value.length} 个用户关联吗？`,"确认操作",{type:"warning"}),c.success("批量操作成功"),m()}catch(l){l!=="cancel"&&c.error("批量操作失败")}}),se=l=>{p.size=l,m()},oe=l=>{p.page=l,m()};return _e(()=>{m(),J()}),(l,t)=>{const T=r("el-icon"),L=r("el-input"),F=r("el-col"),E=r("el-option"),P=r("el-select"),f=r("el-button"),re=r("el-row"),h=r("el-table-column"),G=r("el-avatar"),O=r("el-tag"),ie=r("el-table"),ce=r("el-pagination"),q=r("el-form-item"),de=r("el-form"),ue=r("el-dialog"),pe=ge("loading");return v(),U("div",ke,[t[15]||(t[15]=o("div",{class:"page-header"},[o("h2",null,"用户关联管理"),o("p",null,"管理用户之间的关联关系，查看关联状态和解除关联")],-1)),o("div",be,[a(re,{gutter:20},{default:n(()=>[a(F,{span:6},{default:n(()=>[a(L,{modelValue:b.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>b.keyword=e),placeholder:"搜索用户名或手机号",clearable:"",onInput:A},{prefix:n(()=>[a(T,null,{default:n(()=>[a(R(H))]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(F,{span:4},{default:n(()=>[a(P,{modelValue:b.status,"onUpdate:modelValue":t[1]||(t[1]=e=>b.status=e),placeholder:"关联状态",clearable:"",onChange:A},{default:n(()=>[a(E,{label:"待确认",value:"pending"}),a(E,{label:"已接受",value:"accepted"}),a(E,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),a(F,{span:4},{default:n(()=>[a(f,{type:"primary",onClick:A},{default:n(()=>[a(T,null,{default:n(()=>[a(R(H))]),_:1}),t[8]||(t[8]=i(" 搜索 "))]),_:1,__:[8]})]),_:1})]),_:1})]),o("div",Ce,[fe((v(),C(ie,{data:k.value,style:{width:"100%"},onSelectionChange:K},{default:n(()=>[a(h,{type:"selection",width:"55"}),a(h,{label:"发起用户","min-width":"120"},{default:n(({row:e})=>{var y,x,I;return[o("div",we,[a(G,{size:32,src:(y=e.sender)==null?void 0:y.avatar},{default:n(()=>{var S,B;return[i(d((B=(S=e.sender)==null?void 0:S.name)==null?void 0:B.charAt(0)),1)]}),_:2},1032,["src"]),o("div",ze,[o("div",Ve,d((x=e.sender)==null?void 0:x.name),1),o("div",xe,d(((I=e.sender)==null?void 0:I.phone)||"未绑定"),1)])])]}),_:1}),a(h,{label:"接收用户","min-width":"120"},{default:n(({row:e})=>{var y,x,I;return[o("div",Ie,[a(G,{size:32,src:(y=e.receiver)==null?void 0:y.avatar},{default:n(()=>{var S,B;return[i(d((B=(S=e.receiver)==null?void 0:S.name)==null?void 0:B.charAt(0)),1)]}),_:2},1032,["src"]),o("div",Se,[o("div",Be,d((x=e.receiver)==null?void 0:x.name),1),o("div",De,d(((I=e.receiver)==null?void 0:I.phone)||"未绑定"),1)])])]}),_:1}),a(h,{label:"关联状态",width:"100"},{default:n(({row:e})=>[a(O,{type:Q(e.status),size:"small"},{default:n(()=>[i(d(W(e.status)),1)]),_:2},1032,["type"])]),_:1}),a(h,{label:"申请消息","min-width":"150"},{default:n(({row:e})=>[o("span",$e,d(e.message||"无"),1)]),_:1}),a(h,{label:"关联备注","min-width":"120"},{default:n(({row:e})=>[o("span",null,d(e.remark||"无"),1)]),_:1}),a(h,{label:"所属分组",width:"100"},{default:n(({row:e})=>[e.group?(v(),C(O,{key:0,size:"small",color:e.group.color},{default:n(()=>[i(d(e.group.name),1)]),_:2},1032,["color"])):(v(),U("span",Ee,"未分组"))]),_:1}),a(h,{label:"创建时间",width:"160"},{default:n(({row:e})=>[i(d(R(ye)(e.createdAt)),1)]),_:1}),a(h,{label:"操作",width:"200",fixed:"right"},{default:n(({row:e})=>[e.status==="pending"?(v(),C(f,{key:0,type:"success",size:"small",onClick:y=>X(e)},{default:n(()=>t[9]||(t[9]=[i(" 通过 ")])),_:2,__:[9]},1032,["onClick"])):j("",!0),e.status==="pending"?(v(),C(f,{key:1,type:"warning",size:"small",onClick:y=>Y(e)},{default:n(()=>t[10]||(t[10]=[i(" 拒绝 ")])),_:2,__:[10]},1032,["onClick"])):j("",!0),e.status==="accepted"?(v(),C(f,{key:2,type:"danger",size:"small",onClick:y=>Z(e)},{default:n(()=>t[11]||(t[11]=[i(" 解除关联 ")])),_:2,__:[11]},1032,["onClick"])):j("",!0),a(f,{type:"primary",size:"small",onClick:y=>ee(e)},{default:n(()=>t[12]||(t[12]=[i(" 编辑 ")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[pe,z.value]]),o("div",Ue,[a(ce,{"current-page":p.page,"onUpdate:currentPage":t[2]||(t[2]=e=>p.page=e),"page-size":p.size,"onUpdate:pageSize":t[3]||(t[3]=e=>p.size=e),total:p.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:se,onCurrentChange:oe},null,8,["current-page","page-size","total"])])]),u.value.length>0?(v(),U("div",je,[a(f,{type:"success",onClick:ae},{default:n(()=>[i(" 批量通过 ("+d(u.value.length)+") ",1)]),_:1}),a(f,{type:"warning",onClick:ne},{default:n(()=>[i(" 批量拒绝 ("+d(u.value.length)+") ",1)]),_:1}),a(f,{type:"danger",onClick:le},{default:n(()=>[i(" 批量解除 ("+d(u.value.length)+") ",1)]),_:1})])):j("",!0),a(ue,{modelValue:s.visible,"onUpdate:modelValue":t[7]||(t[7]=e=>s.visible=e),title:"编辑关联信息",width:"500px"},{footer:n(()=>[a(f,{onClick:t[6]||(t[6]=e=>s.visible=!1)},{default:n(()=>t[13]||(t[13]=[i("取消")])),_:1,__:[13]}),a(f,{type:"primary",onClick:te},{default:n(()=>t[14]||(t[14]=[i("保存")])),_:1,__:[14]})]),default:n(()=>[a(de,{ref_key:"editFormRef",ref:_,model:s.form,rules:V,"label-width":"80px"},{default:n(()=>[a(q,{label:"关联备注",prop:"remark"},{default:n(()=>[a(L,{modelValue:s.form.remark,"onUpdate:modelValue":t[4]||(t[4]=e=>s.form.remark=e),placeholder:"请输入关联备注",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(q,{label:"所属分组",prop:"groupId"},{default:n(()=>[a(P,{modelValue:s.form.groupId,"onUpdate:modelValue":t[5]||(t[5]=e=>s.form.groupId=e),placeholder:"选择分组",clearable:"",style:{width:"100%"}},{default:n(()=>[(v(!0),U(ve,null,he($.value,e=>(v(),C(E,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Me=me(Ae,[["__scopeId","data-v-1a862b97"]]);export{Me as default};
