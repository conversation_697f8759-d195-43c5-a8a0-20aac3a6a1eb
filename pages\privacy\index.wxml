<view class="privacy-container">
  <view class="privacy-header">
    <text class="privacy-title">小楠N 小程序隐私保护指引</text>
    <text class="privacy-subtitle">更新日期：2025-08-05</text>
  </view>

  <scroll-view class="privacy-content" scroll-y="true">
    <!-- 开发者信息 -->
    <view class="section">
      <text class="section-content">
        本指引是小楠N小程序开发者（以下简称"开发者"）为处理你的个人信息而制定。
      </text>
    </view>

    <!-- 开发者处理的信息 -->
    <view class="section">
      <text class="section-title">开发者处理的信息</text>
      <text class="section-content">
        根据法律规定，开发者仅处理实现小程序功能所必要的信息。
      </text>

      <view class="article">
        <text class="article-content">
          •
          开发者将在获取你的明示同意后，收集你的微信昵称、头像，用途是【展示登录头像】
        </text>
      </view>

      <view class="article">
        <text class="article-content">
          • 开发者将在获取你的明示同意后，收集你的手机号，用途是【手机密码登录】
        </text>
      </view>

      <view class="article">
        <text class="article-content">
          • 开发者收集你选中的照片或视频信息，用于展示菜图片
        </text>
      </view>

      <view class="article">
        <text class="article-content">
          • 开发者收集你的邮箱，用于邮箱密码登录
        </text>
      </view>
    </view>

    <!-- 你的权益 -->
    <view class="section">
      <text class="section-title">你的权益</text>

      <view class="article">
        <text class="article-content">
          关于收集你的微信昵称、头像、收集你的手机号、收集你选中的照片或视频信息，你可以通过以下路径：
        </text>
        <text class="article-content highlight">
          小程序主页右上角"..." — "设置" — "小程序已获取的信息" — 点击特定信息 —
          点击"通知开发者删除"
        </text>
        <text class="article-content">
          开发者承诺收到通知后将删除信息。法律法规另有规定的，开发者承诺将停止除存储和采取必要的安全保护措施之外的处理。
        </text>
      </view>

      <view class="article">
        <text class="article-content">
          关于你的个人信息，你可以通过以下方式与开发者联系，行使查阅、复制、更正、删除等法定权利。
        </text>
      </view>

      <view class="article">
        <text class="article-content">
          若你在小程序中注册了账号，你可以通过以下方式与开发者联系，申请注销你在小程序中使用的账号。在受理你的申请后，开发者承诺在十五个工作日内完成核查和处理，并按照法律法规要求处理你的相关信息。
        </text>
      </view>
    </view>

    <!-- 开发者对信息的存储 -->
    <view class="section">
      <text class="section-title">开发者对信息的存储</text>
      <text class="article-content">
        开发者承诺，除法律法规另有规定外，开发者对你的信息的保存期限应当为实现处理目的所必要的最短时间。
      </text>
    </view>

    <!-- 信息的使用规则 -->
    <view class="section">
      <text class="section-title">信息的使用规则</text>
      <text class="article-content">
        开发者将会在本指引所明示的用途内使用收集的信息。
      </text>
      <text class="article-content">
        如开发者使用你的信息超出本指引目的或合理范围，开发者必须在变更使用目的或范围前，再次以用户协议方式告知并征得你的明示同意。
      </text>
    </view>

    <!-- 信息对外提供 -->
    <view class="section">
      <text class="section-title">信息对外提供</text>
      <text class="article-content">
        开发者承诺，不会主动共享或转让你的信息至任何第三方，如存在确需共享或转让时，开发者应当直接征得或确认第三方征得你的单独同意。
      </text>
      <text class="article-content">
        开发者承诺，不会对外公开披露你的信息，如必须公开披露时，开发者应当向你告知公开披露的目的、披露信息的类型及可能涉及的信息，并征得你的单独同意。
      </text>
    </view>

    <!-- 联系方式 -->
    <view class="section">
      <text class="section-title">联系方式</text>
      <view class="article">
        <text class="article-content">
          你认为开发者未遵守上述约定，或有其他的投诉建议、或未成年人个人信息保护相关问题，可通过以下方式与开发者联系；或者向微信进行投诉。
        </text>
        <text class="article-content contact"> 邮箱：<EMAIL> </text>
      </view>
    </view>
  </scroll-view>
</view>
