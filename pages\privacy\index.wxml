<view class="privacy-container">
  <view class="privacy-header">
    <text class="privacy-title">用户协议和隐私政策</text>
    <text class="privacy-subtitle">最后更新时间：2024年8月5日</text>
  </view>

  <scroll-view class="privacy-content" scroll-y="true">
    <!-- 用户协议部分 -->
    <view class="section">
      <text class="section-title">一、用户协议</text>
      
      <view class="article">
        <text class="article-title">1.1 服务说明</text>
        <text class="article-content">
          楠楠家厨小程序（以下简称"本服务"）是一款提供家庭厨房服务的微信小程序。通过本服务，用户可以浏览菜品、查看菜单、预订服务等。
        </text>
      </view>

      <view class="article">
        <text class="article-title">1.2 用户注册</text>
        <text class="article-content">
          用户通过微信授权登录即可使用本服务。用户应确保提供的信息真实、准确、完整。
        </text>
      </view>

      <view class="article">
        <text class="article-title">1.3 用户行为规范</text>
        <text class="article-content">
          用户在使用本服务时，应遵守相关法律法规，不得发布违法、有害、虚假信息，不得恶意攻击系统或干扰他人正常使用。
        </text>
      </view>

      <view class="article">
        <text class="article-title">1.4 服务变更</text>
        <text class="article-content">
          我们保留随时修改或中断服务的权利，恕不另行通知。我们不对因服务变更给用户造成的损失承担责任。
        </text>
      </view>
    </view>

    <!-- 隐私政策部分 -->
    <view class="section">
      <text class="section-title">二、隐私政策</text>
      
      <view class="article">
        <text class="article-title">2.1 信息收集</text>
        <text class="article-content">
          我们可能收集以下信息：
          • 微信用户基本信息（头像、昵称、openid）
          • 用户主动提供的个人信息（姓名、手机号、邮箱等）
          • 使用服务时产生的数据（浏览记录、订单信息等）
        </text>
      </view>

      <view class="article">
        <text class="article-title">2.2 信息使用</text>
        <text class="article-content">
          我们收集的信息将用于：
          • 提供和改善服务
          • 用户身份验证
          • 客户服务和技术支持
          • 发送服务相关通知
        </text>
      </view>

      <view class="article">
        <text class="article-title">2.3 信息保护</text>
        <text class="article-content">
          我们采用行业标准的安全措施保护用户信息，包括数据加密、访问控制等。我们不会向第三方出售、交易或转让用户个人信息。
        </text>
      </view>

      <view class="article">
        <text class="article-title">2.4 信息共享</text>
        <text class="article-content">
          除以下情况外，我们不会与第三方共享用户信息：
          • 获得用户明确同意
          • 法律法规要求
          • 保护我们的权利和财产
        </text>
      </view>

      <view class="article">
        <text class="article-title">2.5 用户权利</text>
        <text class="article-content">
          用户有权：
          • 查询、更正、删除个人信息
          • 撤回授权同意
          • 投诉举报
          如需行使上述权利，请联系我们。
        </text>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="section">
      <text class="section-title">三、联系我们</text>
      <view class="article">
        <text class="article-content">
          如果您对本协议和隐私政策有任何疑问，请通过以下方式联系我们：
          • 小程序内客服功能
          • 邮箱：<EMAIL>
        </text>
      </view>
    </view>

    <!-- 生效时间 -->
    <view class="section">
      <text class="section-title">四、生效时间</text>
      <view class="article">
        <text class="article-content">
          本协议和隐私政策自2024年8月5日起生效。我们保留随时更新本协议的权利，更新后的协议将在小程序内公布。
        </text>
      </view>
    </view>
  </scroll-view>

  <!-- 底部按钮 -->
  <view class="privacy-footer">
    <button class="btn-primary" bind:tap="goBack">我已阅读</button>
  </view>
</view>
