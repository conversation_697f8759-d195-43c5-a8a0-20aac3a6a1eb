import{a4 as s}from"./index-CtHojCwd.js";const g={getMessages:e=>s.get("/messages",e),createMessage:e=>s.post("/messages",e),updateMessage:(e,a)=>s.put(`/messages/${e}`,a),deleteMessage:e=>s.delete(`/messages/${e}`),markAsRead:e=>s.put(`/messages/${e}`,{read:!0}),getMessageStatistics:()=>s.get("/messages/statistics"),batchMarkAsRead:e=>s.post("/messages/batch-read",e),batchDeleteMessages:e=>s.post("/messages/batch-delete",e)};export{g as m};
