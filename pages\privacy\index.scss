/* 全局防溢出 */
page {
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.privacy-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  max-width: 100vw;
}

.privacy-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx 32rpx 32rpx;
  text-align: center;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.privacy-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
}

.privacy-content {
  flex: 1;
  padding: 32rpx;
  padding-bottom: 120rpx; /* 为底部按钮留空间 */
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.article {
  margin-bottom: 24rpx;
}

.article:last-child {
  margin-bottom: 0;
}

.article-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  display: block;
  margin-bottom: 12rpx;
}

.article-content {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.6;
  display: block;
  text-align: justify;
  margin-bottom: 16rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
}

.article-content:last-child {
  margin-bottom: 0;
}

.article-content.highlight {
  background-color: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
  font-weight: 500;
}

.article-content.contact {
  color: #667eea;
  font-weight: 600;
  font-size: 28rpx;
}

.section-content {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.6;
  display: block;
  text-align: justify;
  margin-bottom: 16rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
}

.privacy-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #e9ecef;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  width: 100%;
}

.btn-primary {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.btn-primary:active {
  opacity: 0.8;
}
