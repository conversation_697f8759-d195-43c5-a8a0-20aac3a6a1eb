import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import router from '@/router';

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const wholeMenus = ref([]);
  const isMenusLoaded = ref(false);

  // 计算属性
  const menuRoutes = computed(() => {
    return wholeMenus.value;
  });

  // 初始化菜单
  const initMenus = () => {
    if (isMenusLoaded.value) return;

    // 从路由配置中过滤出需要显示在菜单中的路由
    const routes = router.options.routes;
    const menuRoutes = [];

    routes.forEach(route => {
      // 只显示有Layout的路由，排除登录相关页面和隐藏的路由
      if (
        route.component?.name === 'Layout' &&
        !route.meta?.hidden &&
        route.path !== '/login' &&
        route.path !== '/register' &&
        route.path !== '/forgot-password'
      ) {
        // 过滤子路由，移除不需要的页面
        if (route.children) {
          const filteredChildren = route.children.filter(child => {
            // 排除隐藏的路由和演示页面
            return (
              !child.meta?.hidden &&
              !child.path.includes('demo') &&
              !child.path.includes('404') &&
              !child.name?.includes('Demo') &&
              child.meta?.title !== '演示页面' &&
              child.meta?.title !== '页面未找到'
            );
          });

          if (filteredChildren.length > 0) {
            menuRoutes.push({
              ...route,
              children: filteredChildren
            });
          }
        } else if (
          route.meta?.title &&
          !route.meta?.hidden &&
          route.path !== '/login' &&
          route.path !== '/register' &&
          route.path !== '/forgot-password'
        ) {
          menuRoutes.push(route);
        }
      }
    });

    wholeMenus.value = menuRoutes;
    isMenusLoaded.value = true;
  };

  // 重置菜单
  const resetMenus = () => {
    wholeMenus.value = [];
    isMenusLoaded.value = false;
  };

  return {
    wholeMenus,
    menuRoutes,
    isMenusLoaded,
    initMenus,
    resetMenus
  };
});

// 导出hook函数以保持兼容性
export const usePermissionStoreHook = () => {
  const store = usePermissionStore();

  // 如果菜单还没有初始化，则初始化
  if (!store.isMenusLoaded) {
    store.initMenus();
  }

  return store;
};
