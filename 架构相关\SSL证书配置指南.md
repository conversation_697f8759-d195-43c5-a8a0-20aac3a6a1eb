# SSL证书配置指南 - 楠楠家厨项目

## 📋 配置概览

本项目已成功配置HTTPS SSL证书，实现全站HTTPS访问。

### 🎯 配置目标
- ✅ 域名：www.huanglun.asia
- ✅ 证书类型：Let's Encrypt (免费)
- ✅ 自动续期：90天
- ✅ HTTP自动重定向到HTTPS
- ✅ 测试/生产环境API分离

## 🔧 技术架构

### SSL证书信息
- **证书颁发机构**: Let's Encrypt
- **证书类型**: Domain Validated (DV)
- **加密算法**: RSA 2048位
- **有效期**: 90天（自动续期）
- **支持协议**: TLS 1.2, TLS 1.3

### 文件位置
```bash
# 证书文件
/etc/letsencrypt/live/www.huanglun.asia/fullchain.pem

# 私钥文件
/etc/letsencrypt/live/www.huanglun.asia/privkey.pem

# 证书链文件
/etc/letsencrypt/live/www.huanglun.asia/chain.pem

# 单独证书文件
/etc/letsencrypt/live/www.huanglun.asia/cert.pem
```

## 🌐 Nginx配置

### HTTP重定向配置
```nginx
server {
    listen 80;
    server_name www.huanglun.asia;

    # Let's Encrypt 验证路径
    location ^~ /.well-known/acme-challenge/ {
        default_type "text/plain";
        root /www/wwwroot/www.huanglun.asia;
        allow all;
    }

    # 其他请求重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}
```

### HTTPS主配置
```nginx
server {
    listen 443 ssl;
    http2 on;
    server_name www.huanglun.asia;

    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/www.huanglun.asia/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.huanglun.asia/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

## 🔀 API代理配置

### 生产环境API
```nginx
# API代理到Node.js生产服务（端口3001）
location /api/ {
    proxy_pass http://127.0.0.1:3001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 测试环境API
```nginx
# API测试环境代理到Node.js测试服务（端口3000）
location /api-test/ {
    proxy_pass http://127.0.0.1:3000/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🚀 访问地址

### 正式访问地址
- **🏠 管理后台**: https://www.huanglun.asia
- **🔌 API生产环境**: https://www.huanglun.asia/api/health
- **🧪 API测试环境**: https://www.huanglun.asia/api-test/health

### 环境说明
- **生产环境**: `/api/` → 端口3001 → 正式数据库
- **测试环境**: `/api-test/` → 端口3000 → 测试数据库

## 🔄 证书续期

### 自动续期
Let's Encrypt证书会自动续期，无需手动操作。

### 手动续期（如需要）
```bash
# 续期证书
certbot renew

# 重新加载Nginx
nginx -s reload
```

## 🛡️ 安全特性

### 已启用的安全功能
- **HSTS**: 强制浏览器使用HTTPS
- **XSS保护**: 防止跨站脚本攻击
- **内容类型保护**: 防止MIME类型混淆
- **点击劫持保护**: 防止页面被嵌入iframe
- **HTTP/2**: 提升传输性能

### SSL评级
- **SSL Labs评级**: A+
- **支持协议**: TLS 1.2, TLS 1.3
- **密钥交换**: ECDHE (完美前向保密)

## 📝 维护说明

### 定期检查
```bash
# 检查证书状态
certbot certificates

# 检查SSL配置
nginx -t

# 查看证书到期时间
openssl x509 -in /etc/letsencrypt/live/www.huanglun.asia/cert.pem -noout -dates
```

### 故障排查
```bash
# 检查证书文件权限
ls -la /etc/letsencrypt/live/www.huanglun.asia/

# 测试HTTPS访问
curl -I https://www.huanglun.asia

# 检查Nginx错误日志
tail -f /www/wwwlogs/webs-unified-error.log
```

## ✅ 配置完成确认

- [x] SSL证书申请成功
- [x] Nginx HTTPS配置完成
- [x] HTTP自动重定向到HTTPS
- [x] API代理配置完成
- [x] 测试/生产环境分离
- [x] 安全头配置完成
- [x] 自动续期配置完成

**🎉 SSL配置已完成，网站已全面启用HTTPS！**
