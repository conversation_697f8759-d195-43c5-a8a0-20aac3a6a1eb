.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  text-align: center;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .page-subtitle {
    font-size: 28rpx;
    opacity: 0.9;
    margin-bottom: 16rpx;
  }

  .required-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    opacity: 0.9;
    margin-top: 16rpx;

    .required-text {
      margin-left: 8rpx;
    }
  }
}

.profile-form {
  padding: 40rpx 20rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;

  .avatar-container {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    overflow: hidden;
    border: 6rpx solid #fff;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

    .avatar-image {
      width: 100%;
      height: 100%;
    }

    .avatar-placeholder {
      width: 100%;
      height: 100%;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .avatar-edit-icon {
      position: absolute;
      bottom: 8rpx;
      right: 8rpx;
      width: 48rpx;
      height: 48rpx;
      background-color: #1989fa;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 4rpx solid #fff;
    }
  }

  .avatar-tip {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #969799;
  }
}

.save-section {
  margin-top: 60rpx;
  padding: 0 20rpx;
}

.gender-options {
  padding: 20rpx 0;

  .gender-option {
    padding: 30rpx 40rpx;
    font-size: 32rpx;
    text-align: center;
    border-bottom: 1rpx solid #ebedf0;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f2f3f5;
    }
  }
}

.avatar-actions {
  padding: 20rpx 0;

  .avatar-action {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 40rpx;
    font-size: 32rpx;

    text {
      margin-left: 20rpx;
    }

    &:active {
      background-color: #f2f3f5;
    }
  }
}

/* 原生 picker 样式优化 */
picker {
  display: block;
  width: 100%;
}

/* Vant 组件样式覆盖 */
.van-cell-group {
  margin-bottom: 30rpx;

  .van-cell-group__title {
    font-size: 28rpx;
    font-weight: bold;
    color: #323233;
    padding-left: 0;
  }
}

.van-field {
  .van-field__label {
    font-size: 28rpx;
    color: #646566;
  }

  .van-field__control {
    font-size: 28rpx;
  }
}

.van-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.skip-section {
  margin-top: 24rpx;

  .skip-button {
    background-color: #f7f8fa !important;
    color: #969799 !important;
    border: 2rpx solid #ebedf0 !important;
    border-radius: 12rpx !important;
    height: 88rpx !important;
    font-size: 28rpx !important;
  }

  .skip-tip {
    text-align: center;
    font-size: 24rpx;
    color: #969799;
    margin-top: 16rpx;
    line-height: 1.4;
  }
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.van-loading {
  padding: 100rpx 0;
  text-align: center;
}
