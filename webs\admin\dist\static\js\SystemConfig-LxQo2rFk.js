import{c as r,d as t,h as o,ac as k,ad as f,_ as w,e as g,i as d,t as a,N as x,O as m,x as b}from"./index-CtHojCwd.js";function L(i,s){return o(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function C(i,s){return o(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"})])}function R(i,s){return o(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"})])}function p(){const i=k(),s=f();return{server:i,serverType:s,api:{baseURL:i.baseURL,timeout:i.timeout,uploadURL:`${i.baseURL}/upload`,downloadURL:`${i.baseURL}/download`,staticURL:`${i.baseURL}/static`},websocket:{url:i.baseURL.replace(/^http/,"ws").replace("/api","/ws"),reconnectInterval:5e3,maxReconnectAttempts:5},app:{name:"楠楠厨房后台管理系统",version:"2.0.0",description:"基于 Vue 3 + Tailwind CSS 的现代化后台管理系统",copyright:"©楠楠厨房. All rights reserved."},features:{debug:s!=="production",performance:s==="production",errorReporting:s==="production",hotReload:s==="dev",devtools:s!=="production",mock:s==="dev",cache:!0,compression:s==="production"},storage:{tokenKey:"nannan_admin_token",userKey:"nannan_admin_user",settingsKey:"nannan_admin_settings",themeKey:"nannan_admin_theme",localeKey:"nannan_admin_locale"},pagination:{defaultPageSize:10,pageSizes:[10,20,50,100],maxPageSize:1e3},upload:{maxSize:10,allowedTypes:{image:["jpg","jpeg","png","gif","webp"],document:["pdf","doc","docx","xls","xlsx","ppt","pptx"],video:["mp4","avi","mov","wmv"],audio:["mp3","wav","flac"]},uploadPath:"/upload",multiple:!0,maxCount:10},security:{tokenExpiry:24,passwordMinLength:6,passwordMaxLength:20,maxLoginAttempts:5,lockoutDuration:30},theme:{default:"light",available:["light","dark","auto"],primaryColor:"#3b82f6",animations:!0,transitions:!0}}}p();const S={class:"system-config-page"},K={class:"space-y-6"},U={class:"card"},z={class:"card-body"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},B={class:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg"},N={class:"flex items-center"},T={class:"flex-shrink-0"},j={class:"ml-3"},A={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},$={class:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg"},E={class:"flex items-center"},P={class:"flex-shrink-0"},D={class:"ml-3"},H={class:"text-sm font-mono text-green-600 dark:text-green-400 break-all"},I={class:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg"},V={class:"flex items-center"},Z={class:"flex-shrink-0"},F={class:"ml-3"},O={class:"text-lg font-semibold text-yellow-600 dark:text-yellow-400"},q={class:"card"},G={class:"card-body"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"mt-1 text-sm text-gray-900 dark:text-white"},W={class:"mt-1 text-sm text-gray-900 dark:text-white"},X={class:"mt-1 text-sm text-gray-900 dark:text-white"},Y={class:"mt-1 text-sm text-gray-900 dark:text-white"},tt={class:"card"},et={class:"card-body"},st={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},dt={class:"text-sm font-medium text-gray-900 dark:text-white capitalize"},at={class:"card"},rt={class:"card-body"},ot={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},it={class:"text-sm font-medium text-gray-500 dark:text-gray-400 capitalize"},nt={class:"mt-1 text-sm font-mono text-gray-900 dark:text-white"},lt={class:"card"},ct={class:"card-body"},xt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},mt={class:"mt-1 text-sm text-gray-900 dark:text-white"},gt={class:"mt-1 text-sm text-gray-900 dark:text-white"},ut={class:"mt-1 text-sm text-gray-900 dark:text-white"},pt={class:"mt-6"},yt={class:"space-y-2"},ht={class:"text-sm font-medium text-gray-700 dark:text-gray-300 w-20 capitalize"},vt={class:"flex flex-wrap gap-1"},_t={class:"card"},kt={class:"card-body"},ft={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},wt={class:"mt-1 text-sm text-gray-900 dark:text-white"},bt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Lt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Ct={class:"mt-1 text-sm text-gray-900 dark:text-white"},Rt={__name:"SystemConfig",setup(i){const s=p(),y={debug:"调试模式",performance:"性能监控",errorReporting:"错误上报",hotReload:"热更新",devtools:"开发工具",mock:"Mock 数据",cache:"缓存",compression:"压缩"},h={tokenKey:"Token 存储键",userKey:"用户信息存储键",settingsKey:"设置存储键",themeKey:"主题存储键",localeKey:"语言存储键"},v=l=>y[l]||l,_=l=>h[l]||l;return(l,e)=>(o(),r("div",S,[e[21]||(e[21]=t("div",{class:"page-header"},[t("h2",{class:"page-title"},"系统配置"),t("p",{class:"page-subtitle"},"查看和管理系统配置信息")],-1)),t("div",K,[t("div",U,[e[3]||(e[3]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 服务器配置 "),t("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 当前连接的服务器信息 ")],-1)),t("div",z,[t("div",M,[t("div",B,[t("div",N,[t("div",T,[g(d(R),{class:"h-4 w-4 text-blue-600",style:{width:"16px",height:"16px"}})]),t("div",j,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-blue-900 dark:text-blue-200"}," 服务器环境 ",-1)),t("p",A,a(d(s).server.name),1)])])]),t("div",$,[t("div",E,[t("div",P,[g(d(C),{class:"h-4 w-4 text-green-600",style:{width:"16px",height:"16px"}})]),t("div",D,[e[1]||(e[1]=t("p",{class:"text-sm font-medium text-green-900 dark:text-green-200"}," API 地址 ",-1)),t("p",H,a(d(s).server.baseURL),1)])])]),t("div",I,[t("div",V,[t("div",Z,[g(d(L),{class:"h-4 w-4 text-yellow-600",style:{width:"16px",height:"16px"}})]),t("div",F,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-yellow-900 dark:text-yellow-200"}," 请求超时 ",-1)),t("p",O,a(d(s).server.timeout)+"ms ",1)])])])])])]),t("div",q,[e[8]||(e[8]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 应用信息 ")],-1)),t("div",G,[t("dl",J,[t("div",null,[e[4]||(e[4]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 应用名称 ",-1)),t("dd",Q,a(d(s).app.name),1)]),t("div",null,[e[5]||(e[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 版本号 ",-1)),t("dd",W,a(d(s).app.version),1)]),t("div",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 描述 ",-1)),t("dd",X,a(d(s).app.description),1)]),t("div",null,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 版权信息 ",-1)),t("dd",Y,a(d(s).app.copyright),1)])])])]),t("div",tt,[e[9]||(e[9]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 功能开关 "),t("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," 当前环境下的功能启用状态 ")],-1)),t("div",et,[t("div",st,[(o(!0),r(x,null,m(d(s).features,(c,n)=>(o(),r("div",{key:n,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"},[t("span",dt,a(v(n)),1),t("span",{class:b([c?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200","inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(c?"启用":"禁用"),3)]))),128))])])]),t("div",at,[e[10]||(e[10]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 存储配置 ")],-1)),t("div",rt,[t("dl",ot,[(o(!0),r(x,null,m(d(s).storage,(c,n)=>(o(),r("div",{key:n},[t("dt",it,a(_(n)),1),t("dd",nt,a(c),1)]))),128))])])]),t("div",lt,[e[15]||(e[15]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 上传配置 ")],-1)),t("div",ct,[t("div",xt,[t("div",null,[e[11]||(e[11]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 最大文件大小 ",-1)),t("dd",mt,a(d(s).upload.maxSize)+" MB ",1)]),t("div",null,[e[12]||(e[12]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 最大上传数量 ",-1)),t("dd",gt,a(d(s).upload.maxCount)+" 个 ",1)]),t("div",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 多文件上传 ",-1)),t("dd",ut,a(d(s).upload.multiple?"支持":"不支持"),1)])]),t("div",pt,[e[14]||(e[14]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-3"}," 支持的文件类型 ",-1)),t("div",yt,[(o(!0),r(x,null,m(d(s).upload.allowedTypes,(c,n)=>(o(),r("div",{key:n,class:"flex items-center"},[t("span",ht,a(n)+":",1),t("div",vt,[(o(!0),r(x,null,m(c,u=>(o(),r("span",{key:u,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"}," ."+a(u),1))),128))])]))),128))])])])]),t("div",_t,[e[20]||(e[20]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," 安全配置 ")],-1)),t("div",kt,[t("dl",ft,[t("div",null,[e[16]||(e[16]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," Token 过期时间 ",-1)),t("dd",wt,a(d(s).security.tokenExpiry)+" 小时 ",1)]),t("div",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 密码长度范围 ",-1)),t("dd",bt,a(d(s).security.passwordMinLength)+"-"+a(d(s).security.passwordMaxLength)+" 位 ",1)]),t("div",null,[e[18]||(e[18]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 最大登录失败次数 ",-1)),t("dd",Lt,a(d(s).security.maxLoginAttempts)+" 次 ",1)]),t("div",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"}," 账户锁定时间 ",-1)),t("dd",Ct,a(d(s).security.lockoutDuration)+" 分钟 ",1)])])])])])]))}},Kt=w(Rt,[["__scopeId","data-v-5f0444fc"]]);export{Kt as default};
