#!/usr/bin/env node
/**
 * 强制清空测试数据库脚本（无需确认）
 * 快速清理测试环境的数据
 */

const {PrismaClient} = require('@prisma/client');

// 使用测试数据库连接
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'mysql://nannan_user:5201314hl@8.148.231.104:3306/nannan_db_test'
    }
  }
});

async function clearTestDatabase() {
  try {
    console.log('🗑️  开始清空测试数据库 nannan_db_test...');

    // 禁用外键检查
    console.log('🔧 禁用外键检查...');
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0`;

    // 按照外键依赖关系的顺序删除数据
    console.log('📋 删除菜单项...');
    const menuItems = await prisma.menuItem.deleteMany({});
    console.log(`   删除了 ${menuItems.count} 条菜单项`);

    console.log('📋 删除推荐菜单...');
    const recommendedMenus = await prisma.recommendedMenu.deleteMany({});
    console.log(`   删除了 ${recommendedMenus.count} 条推荐菜单`);

    console.log('📋 删除菜单...');
    const menus = await prisma.menu.deleteMany({});
    console.log(`   删除了 ${menus.count} 条菜单`);

    console.log('📦 删除订单推送...');
    const orderPushes = await prisma.orderPush.deleteMany({});
    console.log(`   删除了 ${orderPushes.count} 条订单推送`);

    console.log('📦 删除订单...');
    const orders = await prisma.order.deleteMany({});
    console.log(`   删除了 ${orders.count} 条订单`);

    console.log('🔔 删除通知...');
    const notifications = await prisma.notification.deleteMany({});
    console.log(`   删除了 ${notifications.count} 条通知`);

    console.log('💬 删除消息接收者...');
    const messageRecipients = await prisma.messageRecipient.deleteMany({});
    console.log(`   删除了 ${messageRecipients.count} 条消息接收者`);

    console.log('💬 删除消息...');
    const messages = await prisma.message.deleteMany({});
    console.log(`   删除了 ${messages.count} 条消息`);

    console.log('🔗 删除用户连接...');
    const connections = await prisma.userConnection.deleteMany({});
    console.log(`   删除了 ${connections.count} 条用户连接`);

    console.log('🍽️  删除菜品...');
    const dishes = await prisma.dish.deleteMany({});
    console.log(`   删除了 ${dishes.count} 条菜品`);

    console.log('📂 删除菜品分类...');
    const categories = await prisma.category.deleteMany({});
    console.log(`   删除了 ${categories.count} 条菜品分类`);

    console.log('👥 删除普通用户（保留管理员）...');
    const users = await prisma.user.deleteMany({
      where: {
        role: {
          not: 'admin'
        }
      }
    });
    console.log(`   删除了 ${users.count} 个普通用户`);

    // 重新启用外键检查
    console.log('🔧 重新启用外键检查...');
    await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1`;

    console.log('✅ 测试数据库清空完成！');

    // 显示剩余数据统计
    const remainingUsers = await prisma.user.count();
    const remainingDishes = await prisma.dish.count();
    const remainingOrders = await prisma.order.count();
    const remainingMessages = await prisma.message.count();

    console.log('\n📊 剩余数据统计:');
    console.log(`👥 用户: ${remainingUsers}`);
    console.log(`🍽️  菜品: ${remainingDishes}`);
    console.log(`📦 订单: ${remainingOrders}`);
    console.log(`💬 消息: ${remainingMessages}`);

    return {
      deleted: {
        menuItems: menuItems.count,
        recommendedMenus: recommendedMenus.count,
        menus: menus.count,
        orderPushes: orderPushes.count,
        orders: orders.count,
        notifications: notifications.count,
        messageRecipients: messageRecipients.count,
        messages: messages.count,
        connections: connections.count,
        dishes: dishes.count,
        categories: categories.count,
        users: users.count
      },
      remaining: {
        users: remainingUsers,
        dishes: remainingDishes,
        orders: remainingOrders,
        messages: remainingMessages
      }
    };
  } catch (error) {
    console.error('❌ 清空数据库失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 主函数
async function main() {
  try {
    console.log('🔍 连接到测试数据库: nannan_db_test');
    console.log('⚠️  即将清空测试数据库的所有数据...\n');

    const result = await clearTestDatabase();

    console.log('\n🎉 测试数据库清空成功！');
    console.log(
      '📈 总计删除:',
      Object.values(result.deleted).reduce((a, b) => a + b, 0),
      '条记录'
    );
  } catch (error) {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  }
}

// 运行脚本
main();
