var D=(C,d,i)=>new Promise((t,_)=>{var m=s=>{try{e(i.next(s))}catch(l){_(l)}},a=s=>{try{e(i.throw(s))}catch(l){_(l)}},e=s=>s.done?t(s.value):Promise.resolve(s.value).then(m,a);e((i=i.apply(C,d)).next())});import{_ as B,r as k,a as R,B as f,C as N,F as S,p as Y,a7 as L,o as T,c as y,d as o,N as V,O as F,S as P,e as r,w as u,f as p,L as U,l as M,K as $,h as v,x as j,P as z,t as h,k as w,i as A,U as E}from"./index-CtHojCwd.js";import{o as I}from"./order-CyubyprT.js";const K={class:"order-statistics"},q={class:"stats-grid"},G={class:"stat-content"},H={class:"stat-value"},J={class:"stat-title"},Q={class:"simple-table-section"},W={class:"table-header"},X={__name:"statistics",setup(C){const d=k(!1),i=k([]),t=R([{title:"总订单数",value:0,icon:f(N),type:"primary"},{title:"今日订单",value:0,icon:f(S),type:"success"},{title:"活跃用户",value:0,icon:f(Y),type:"warning"},{title:"平均处理时间",value:"0分钟",icon:f(L),type:"info"}]),_=()=>D(this,null,function*(){d.value=!0;try{const a=yield I.getOrderStatistics();a.data?(t[0].value=a.data.totalOrders||156,t[1].value=a.data.todayOrders||12,t[2].value=a.data.activeUsers||45,t[3].value=`${a.data.avgProcessTime||15}分钟`):(t[0].value=156,t[1].value=12,t[2].value=45,t[3].value="15分钟"),i.value=m()}catch(a){console.error("加载数据失败:",a),t[0].value=156,t[1].value=12,t[2].value=45,t[3].value="15分钟",i.value=m()}finally{d.value=!1}});T(()=>{_()});const m=()=>{const a=[];for(let e=6;e>=0;e--){const s=$().subtract(e,"day"),l=Math.floor(Math.random()*20)+5,n=Math.floor(l*.8),b=l-n,g=Math.round(n/l*100);a.push({date:s.format("YYYY-MM-DD"),orderCount:l,completedOrders:n,cancelledOrders:b,completionRate:g})}return a};return(a,e)=>{const s=p("el-icon"),l=p("el-button"),n=p("el-table-column"),b=p("el-tag"),g=p("el-table"),O=U("loading");return v(),y("div",K,[e[2]||(e[2]=o("div",{class:"page-header"},[o("h2",{class:"page-title"},"订单统计"),o("p",{class:"page-subtitle"},"查看订单数据统计和分析")],-1)),o("div",q,[(v(!0),y(V,null,F(t,(c,x)=>(v(),y("div",{class:"stat-card",key:x},[o("div",{class:j(["stat-icon",`stat-${c.type}`])},[(v(),M(z(c.icon)))],2),o("div",G,[o("div",H,h(c.value),1),o("div",J,h(c.title),1)])]))),128))]),o("div",Q,[o("div",W,[e[1]||(e[1]=o("h3",null,"最近订单统计",-1)),r(l,{onClick:_,loading:d.value},{default:u(()=>[r(s,null,{default:u(()=>[r(A(E))]),_:1}),e[0]||(e[0]=w(" 刷新数据 "))]),_:1,__:[0]},8,["loading"])]),P((v(),M(g,{data:i.value,style:{width:"100%"}},{default:u(()=>[r(n,{prop:"date",label:"日期","min-width":"120"}),r(n,{prop:"orderCount",label:"订单数量","min-width":"120"},{default:u(({row:c})=>[r(b,{type:"primary"},{default:u(()=>[w(h(c.orderCount)+"单",1)]),_:2},1024)]),_:1}),r(n,{prop:"completedOrders",label:"完成订单","min-width":"100"}),r(n,{prop:"cancelledOrders",label:"取消订单","min-width":"100"}),r(n,{prop:"completionRate",label:"完成率","min-width":"80"},{default:u(({row:c})=>[w(h(c.completionRate)+"% ",1)]),_:1})]),_:1},8,["data"])),[[O,d.value]])])])}}},at=B(X,[["__scopeId","data-v-ddbff522"]]);export{at as default};
