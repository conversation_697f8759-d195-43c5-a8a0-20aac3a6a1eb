<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view
      class="page-title"
      >{{ isRequired ? '完善个人信息' : '个人信息' }}</view
    >
    <view
      class="page-subtitle"
      >{{ isRequired ? '设置账户登录信息' : '管理您的账户信息' }}</view
    >
    <view wx:if="{{ isRequired }}" class="required-tip">
      <van-icon name="info-o" size="16rpx" color="#ff6b6b" />
      <text class="required-text">请设置登录所需的基本信息</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <van-loading type="spinner" color="#1989fa" vertical>
      加载中...
    </van-loading>
  </view>

  <!-- 用户信息表单 -->
  <view wx:else class="profile-form">
    <!-- 头像部分 -->
    <view class="avatar-section">
      <view class="avatar-container" bind:tap="showAvatarActions">
        <safe-image
          wx:if="{{userInfo.avatar}}"
          src="{{userInfo.avatar}}"
          imageType="avatar"
          custom-class="avatar-container"
          image-class="avatar-image"
          mode="aspectFill"
        />
        <view wx:else class="avatar-placeholder">
          <van-icon name="user-o" size="48rpx" color="#999" />
        </view>
        <view class="avatar-edit-icon">
          <van-icon name="edit" size="24rpx" color="#fff" />
        </view>
      </view>
      <view class="avatar-tip">点击更换头像</view>
    </view>

    <!-- 基础信息 -->
    <van-cell-group
      title="{{ isRequired ? '基础信息 (必填)' : '基础信息' }}"
      inset
    >
      <van-field
        value="{{ form.name }}"
        label="{{ isRequired ? '昵称 *' : '昵称' }}"
        placeholder="请输入您的昵称"
        required="{{ isRequired }}"
        bind:change="onNameChange"
        maxlength="10"
        show-word-limit
      />

      <van-field
        value="{{ form.phone }}"
        label="{{ isRequired ? '手机号 *' : '手机号' }}"
        placeholder="{{ isRequired ? '账号登录' : '非必填' }}"
        required="{{ isRequired }}"
        type="number"
        bind:change="onPhoneChange"
        maxlength="11"
      />

      <van-field
        value="{{ form.email }}"
        label="{{ isRequired ? '邮箱 *' : '邮箱' }}"
        placeholder="账号登录"
        required="{{ isRequired }}"
        type="email"
        bind:change="onEmailChange"
        maxlength="50"
      />

      <!-- 密码设置（仅在必填模式且没有手机号时显示） -->
      <van-field
        wx:if="{{ isRequired && needPasswordReset }}"
        value="{{ form.password }}"
        label="设置密码 *"
        placeholder="用于账号登录（6-20位）"
        required="{{ isRequired }}"
        type="password"
        bind:change="onPasswordChange"
        maxlength="20"
        show-word-limit
      />

      <van-field
        wx:if="{{ isRequired && needPasswordReset }}"
        value="{{ form.confirmPassword }}"
        label="确认密码 *"
        placeholder="请再次输入密码"
        required="{{ isRequired }}"
        type="password"
        bind:change="onConfirmPasswordChange"
        maxlength="20"
      />
    </van-cell-group>

    <!-- 详细信息 -->
    <van-cell-group title="详细信息" inset>
      <!-- <van-cell
        title="性别"
        value="{{ genderDisplayText }}"
        is-link
        bind:click="showGenderPicker"
      /> -->

      <!-- <picker
        mode="date"
        value="{{ form.birthday }}"
        start="1900-01-01"
        end="{{ maxDateString }}"
        bindchange="onNativeDateChange"
      >
        <van-cell
          title="生日"
          value="{{ form.birthday || '请选择生日' }}"
          is-link
        />
      </picker> -->
      <!-- <van-field
        value="{{ form.address }}"
        label="地址"
        placeholder="请输入您的地址（可选）"
        bind:change="onAddressChange"
        maxlength="100"
        show-word-limit
      /> -->

      <van-field
        value="{{ form.bio }}"
        label="个人简介"
        placeholder="介绍一下自己吧（可选）"
        type="textarea"
        autosize
        bind:change="onBioChange"
        maxlength="200"
        show-word-limit
      />
    </van-cell-group>

    <!-- 保存按钮 -->
    <view class="save-section">
      <van-button
        type="primary"
        size="large"
        loading="{{ saving }}"
        loading-text="保存中..."
        bind:click="saveUserInfo"
        block
      >
        {{ isRequired ? '完成并继续' : '保存信息' }}
      </van-button>
    </view>
  </view>

  <!-- 性别选择器 -->
  <van-action-sheet
    show="{{ showGenderPicker }}"
    title="选择性别"
    bind:close="onGenderCancel"
  >
    <view class="gender-options">
      <view
        wx:for="{{ genderOptions }}"
        wx:key="value"
        class="gender-option"
        data-value="{{ item.value }}"
        bind:tap="onGenderConfirm"
      >
        {{ item.text }}
      </view>
    </view>
  </van-action-sheet>

  <!-- 头像操作弹窗 -->
  <van-action-sheet
    show="{{ showAvatarActions }}"
    title="更换头像"
    bind:close="hideAvatarActions"
  >
    <view class="avatar-actions">
      <view class="avatar-action" bind:tap="chooseAvatar">
        <van-icon name="photo-o" size="32rpx" />
        <text>选择照片</text>
      </view>
    </view>
  </van-action-sheet>
</view>
