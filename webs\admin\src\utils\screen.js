/**
 * 屏幕尺寸和响应式设计工具
 * 提供屏幕尺寸检测和响应式布局支持
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点定义
export const BREAKPOINTS = {
  xs: 0,      // 超小屏幕
  sm: 576,    // 小屏幕
  md: 768,    // 中等屏幕
  lg: 992,    // 大屏幕
  xl: 1200,   // 超大屏幕
  xxl: 1600   // 超超大屏幕
}

// 设备类型
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop'
}

// 响应式状态管理
class ScreenManager {
  constructor() {
    this.width = ref(window.innerWidth)
    this.height = ref(window.innerHeight)
    
    this.init()
  }

  init() {
    this.handleResize = this.handleResize.bind(this)
    window.addEventListener('resize', this.handleResize)
  }

  handleResize() {
    this.width.value = window.innerWidth
    this.height.value = window.innerHeight
  }

  destroy() {
    window.removeEventListener('resize', this.handleResize)
  }

  // 获取当前断点
  get currentBreakpoint() {
    return computed(() => {
      const w = this.width.value
      if (w >= BREAKPOINTS.xxl) return 'xxl'
      if (w >= BREAKPOINTS.xl) return 'xl'
      if (w >= BREAKPOINTS.lg) return 'lg'
      if (w >= BREAKPOINTS.md) return 'md'
      if (w >= BREAKPOINTS.sm) return 'sm'
      return 'xs'
    })
  }

  // 获取设备类型
  get deviceType() {
    return computed(() => {
      const w = this.width.value
      if (w < BREAKPOINTS.md) return DEVICE_TYPES.MOBILE
      if (w < BREAKPOINTS.lg) return DEVICE_TYPES.TABLET
      return DEVICE_TYPES.DESKTOP
    })
  }

  // 是否为移动设备
  get isMobile() {
    return computed(() => this.deviceType.value === DEVICE_TYPES.MOBILE)
  }

  // 是否为平板设备
  get isTablet() {
    return computed(() => this.deviceType.value === DEVICE_TYPES.TABLET)
  }

  // 是否为桌面设备
  get isDesktop() {
    return computed(() => this.deviceType.value === DEVICE_TYPES.DESKTOP)
  }

  // 是否为小屏幕
  get isSmallScreen() {
    return computed(() => this.width.value < BREAKPOINTS.lg)
  }

  // 是否为大屏幕
  get isLargeScreen() {
    return computed(() => this.width.value >= BREAKPOINTS.lg)
  }

  // 屏幕方向
  get orientation() {
    return computed(() => {
      return this.width.value > this.height.value ? 'landscape' : 'portrait'
    })
  }

  // 是否为横屏
  get isLandscape() {
    return computed(() => this.orientation.value === 'landscape')
  }

  // 是否为竖屏
  get isPortrait() {
    return computed(() => this.orientation.value === 'portrait')
  }
}

// 创建全局屏幕管理器实例
const screenManager = new ScreenManager()

// 导出屏幕检测 hook
export const useScreen = () => {
  return {
    width: screenManager.width,
    height: screenManager.height,
    currentBreakpoint: screenManager.currentBreakpoint,
    deviceType: screenManager.deviceType,
    isMobile: screenManager.isMobile,
    isTablet: screenManager.isTablet,
    isDesktop: screenManager.isDesktop,
    isSmallScreen: screenManager.isSmallScreen,
    isLargeScreen: screenManager.isLargeScreen,
    orientation: screenManager.orientation,
    isLandscape: screenManager.isLandscape,
    isPortrait: screenManager.isPortrait
  }
}

// 断点匹配工具
export const useBreakpoint = () => {
  const { currentBreakpoint } = useScreen()

  const matches = (breakpoint) => {
    return computed(() => {
      const current = currentBreakpoint.value
      const breakpoints = Object.keys(BREAKPOINTS)
      const currentIndex = breakpoints.indexOf(current)
      const targetIndex = breakpoints.indexOf(breakpoint)
      return currentIndex >= targetIndex
    })
  }

  const between = (min, max) => {
    return computed(() => {
      const current = currentBreakpoint.value
      const breakpoints = Object.keys(BREAKPOINTS)
      const currentIndex = breakpoints.indexOf(current)
      const minIndex = breakpoints.indexOf(min)
      const maxIndex = breakpoints.indexOf(max)
      return currentIndex >= minIndex && currentIndex <= maxIndex
    })
  }

  const only = (breakpoint) => {
    return computed(() => currentBreakpoint.value === breakpoint)
  }

  return {
    currentBreakpoint,
    matches,
    between,
    only,
    xs: only('xs'),
    sm: only('sm'),
    md: only('md'),
    lg: only('lg'),
    xl: only('xl'),
    xxl: only('xxl'),
    smAndUp: matches('sm'),
    mdAndUp: matches('md'),
    lgAndUp: matches('lg'),
    xlAndUp: matches('xl'),
    smAndDown: computed(() => !matches('md').value),
    mdAndDown: computed(() => !matches('lg').value),
    lgAndDown: computed(() => !matches('xl').value)
  }
}

// 媒体查询工具
export const useMediaQuery = (query) => {
  const matches = ref(false)
  let mediaQuery = null

  const updateMatches = () => {
    matches.value = mediaQuery.matches
  }

  onMounted(() => {
    mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    mediaQuery.addEventListener('change', updateMatches)
  })

  onUnmounted(() => {
    if (mediaQuery) {
      mediaQuery.removeEventListener('change', updateMatches)
    }
  })

  return matches
}

// 响应式表格列配置
export const getResponsiveTableColumns = (columns) => {
  const { currentBreakpoint } = useScreen()
  
  return computed(() => {
    const current = currentBreakpoint.value
    const breakpoints = Object.keys(BREAKPOINTS)
    const currentIndex = breakpoints.indexOf(current)
    
    return columns.filter(column => {
      if (!column.responsive) return true
      
      const { hideBelow, showOnly } = column.responsive
      
      if (hideBelow) {
        const hideIndex = breakpoints.indexOf(hideBelow)
        return currentIndex >= hideIndex
      }
      
      if (showOnly) {
        return showOnly.includes(current)
      }
      
      return true
    })
  })
}

// 响应式网格配置
export const getResponsiveGridCols = (config) => {
  const { currentBreakpoint } = useScreen()
  
  return computed(() => {
    const current = currentBreakpoint.value
    return config[current] || config.default || 1
  })
}

// 响应式间距配置
export const getResponsiveSpacing = (config) => {
  const { currentBreakpoint } = useScreen()
  
  return computed(() => {
    const current = currentBreakpoint.value
    return config[current] || config.default || 16
  })
}

// 清理资源
export const destroyScreen = () => {
  screenManager.destroy()
}
