<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑用户' : '用户详情'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入用户姓名"
              :disabled="!isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入手机号"
              :disabled="!isEdit"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户角色" prop="role">
            <el-select
              v-model="formData.role"
              placeholder="请选择用户角色"
              :disabled="!isEdit"
              style="width: 100%"
            >
              <el-option label="普通用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择用户状态"
              :disabled="!isEdit"
              style="width: 100%"
            >
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="注册时间">
            <el-input
              :value="formatTime(formData.createdAt)"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最后登录">
            <el-input
              :value="formatTime(formData.lastLoginAt)"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="订单数量">
            <el-input
              :value="formData.orderCount || 0"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消费总额">
            <el-input
              :value="'¥' + (formData.totalAmount || 0)"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="用户头像">
        <div class="avatar-section">
          <el-avatar :src="formData.avatar" :size="80">
            {{ formData.name ? formData.name.charAt(0) : 'U' }}
          </el-avatar>
          <div class="avatar-info">
            <p>{{ formData.avatar ? '已设置头像' : '未设置头像' }}</p>
            <el-button v-if="isEdit" size="small" type="primary">
              更换头像
            </el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="isEdit"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api/user'
import { formatTime } from '@/utils/common'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userId: {
    type: [String, Number],
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref()

const formData = reactive({
  id: null,
  name: '',
  phone: '',
  role: 'user',
  status: 1,
  avatar: '',
  createdAt: null,
  lastLoginAt: null,
  orderCount: 0,
  totalAmount: 0
})

const rules = {
  name: [
    { required: true, message: '请输入用户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择用户状态', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.userId) {
    loadUserData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

const loadUserData = async () => {
  if (!props.userId) return
  
  loading.value = true
  try {
    const result = await userApi.getUserDetail(props.userId)
    if (result.code === 200) {
      Object.assign(formData, result.data)
    } else {
      ElMessage.error(result.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    const result = await userApi.updateUser(formData.id, {
      name: formData.name,
      phone: formData.phone,
      role: formData.role,
      status: formData.status
    })
    
    if (result.code === 200) {
      ElMessage.success('用户信息更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    id: null,
    name: '',
    phone: '',
    role: 'user',
    status: 1,
    avatar: '',
    createdAt: null,
    lastLoginAt: null,
    orderCount: 0,
    totalAmount: 0
  })
}
</script>

<style scoped lang="scss">
.avatar-section {
  @apply flex items-center space-x-4;
  
  .avatar-info {
    p {
      @apply text-sm text-gray-600 mb-2;
    }
  }
}

.dialog-footer {
  @apply text-right;
}
</style>
