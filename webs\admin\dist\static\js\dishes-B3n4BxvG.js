var we=Object.defineProperty,Ae=Object.defineProperties;var $e=Object.getOwnPropertyDescriptors;var pe=Object.getOwnPropertySymbols;var ze=Object.prototype.hasOwnProperty,Se=Object.prototype.propertyIsEnumerable;var me=(m,c,s)=>c in m?we(m,c,{enumerable:!0,configurable:!0,writable:!0,value:s}):m[c]=s,W=(m,c)=>{for(var s in c||(c={}))ze.call(c,s)&&me(m,s,c[s]);if(pe)for(var s of pe(c))Se.call(c,s)&&me(m,s,c[s]);return m},oe=(m,c)=>Ae(m,$e(c));var B=(m,c,s)=>new Promise((A,S)=>{var d=_=>{try{u(s.next(_))}catch(x){S(x)}},i=_=>{try{u(s.throw(_))}catch(x){S(x)}},u=_=>_.done?A(_.value):Promise.resolve(_.value).then(d,i);u((s=s.apply(m,c)).next())});import{V as Me,_ as ue,r as V,G as _e,f as v,c as z,h as $,d as w,m as ee,e,w as l,i as y,H as he,t as D,N as ye,O as be,W as Q,I as xe,X as re,x as De,E as r,a as de,k as h,q as Ue,o as Ie,Y as Fe,Z as Ee,l as ge,$ as se,a0 as Te,a1 as fe,z as Oe}from"./index-CtHojCwd.js";import{C as je}from"./CustomTable-C1GDYDsI.js";import{f as ve,a as ne}from"./common-DyWwJEEp.js";import{d as q}from"./menu-CzZqR-71.js";import{v as Be,i as Ne,d as Re,e as Le}from"./excel-Db4e3gim.js";const Pe=(m,c={})=>Me(W({url:"/upload/image",method:"post",data:m,headers:{"Content-Type":"multipart/form-data"}},c)),We={class:"image-upload"},qe=["multiple"],Ye={key:0,class:"upload-placeholder"},Ge={class:"upload-text"},He={class:"upload-tip"},Je={key:1,class:"image-list"},Xe={class:"image-overlay"},Ze={class:"image-actions"},Ke={key:0,class:"upload-progress"},Qe={key:0,class:"upload-status"},et={class:"upload-message"},tt={__name:"ImageUpload",props:{modelValue:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!1},maxSize:{type:Number,default:5*1024*1024},maxCount:{type:Number,default:9},uploadType:{type:String,default:"general"},entityId:{type:String,default:""},category:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","upload-success","upload-error"],setup(m,{emit:c}){const s=m,A=c,S=V(null),d=V([]),i=V(!1),u=V(!1),_=V(0),x=V(""),U=V("");_e(()=>s.modelValue,n=>{n&&n.length>0?d.value=n.map(o=>({url:typeof o=="string"?o:o.url,name:typeof o=="string"?"":o.name||"",uploading:!1,progress:100})):d.value=[]},{immediate:!0});const F=()=>{var n;if(!s.disabled&&!(!s.multiple&&d.value.length>=1)){if(d.value.length>=s.maxCount){r.warning(`最多只能上传 ${s.maxCount} 张图片`);return}(n=S.value)==null||n.click()}},G=()=>{s.disabled||(i.value=!0)},te=()=>{i.value=!1},f=n=>{if(s.disabled)return;i.value=!1;const o=Array.from(n.dataTransfer.files).filter(b=>b.type.startsWith("image/"));o.length>0&&k(o)},p=n=>{const o=Array.from(n.target.files);k(o),n.target.value=""},k=n=>{if(!s.multiple&&d.value.length>=1){r.warning("只能上传一张图片");return}const o=s.maxCount-d.value.length;n.length>o&&(r.warning(`最多还能上传 ${o} 张图片`),n=n.slice(0,o)),n.forEach(b=>{I(b)&&H(b)})},I=n=>n.type.startsWith("image/")?n.size>s.maxSize?(r.error(`图片大小不能超过 ${ve(s.maxSize)}`),!1):!0:(r.error("只能上传图片文件"),!1),H=n=>B(this,null,function*(){const o={url:URL.createObjectURL(n),name:n.name,uploading:!0,progress:0,file:n};d.value.push(o),E();try{u.value=!0,U.value="正在上传图片...";const b=new FormData;b.append("image",n),b.append("type",s.uploadType),b.append("entityId",s.entityId),b.append("category",s.category);const T=yield Pe(b,{onUploadProgress:R=>{const L=Math.round(R.loaded*100/R.total);o.progress=L,_.value=L}});o.uploading=!1,o.url=T.data.url,o.progress=100,u.value=!1,U.value="上传成功",E(),A("upload-success",T.data),r.success("图片上传成功")}catch(b){console.error("图片上传失败:",b);const T=d.value.indexOf(o);T>-1&&d.value.splice(T,1),u.value=!1,U.value="上传失败",E(),A("upload-error",b),r.error(b.message||"图片上传失败")}}),J=()=>{},X=n=>{d.value.splice(n,1),E()},E=()=>{const n=d.value.filter(o=>!o.uploading&&o.url).map(o=>o.url);A("update:modelValue",n)};return(n,o)=>{const b=v("el-icon"),T=v("el-image"),R=v("el-button"),L=v("el-progress");return $(),z("div",We,[w("div",{class:De(["upload-area",{"drag-over":i.value}]),onClick:F,onDragover:Q(G,["prevent"]),onDragleave:Q(te,["prevent"]),onDrop:Q(f,["prevent"])},[w("input",{ref_key:"fileInput",ref:S,type:"file",accept:"image/*",multiple:m.multiple,class:"hidden",onChange:p},null,40,qe),!d.value.length||m.multiple?($(),z("div",Ye,[e(b,{class:"upload-icon"},{default:l(()=>[e(y(he))]),_:1}),w("div",Ge,[o[0]||(o[0]=w("p",null,"点击或拖拽上传图片",-1)),w("p",He," 支持 JPG、PNG 格式，单个文件不超过 "+D(y(ve)(m.maxSize)),1)])])):ee("",!0),d.value.length?($(),z("div",Je,[($(!0),z(ye,null,be(d.value,(P,le)=>($(),z("div",{key:le,class:"image-item"},[e(T,{src:P.url,alt:P.name,fit:"cover",class:"preview-image","preview-src-list":[P.url]},null,8,["src","alt","preview-src-list"]),w("div",Xe,[w("div",Ze,[e(R,{type:"primary",icon:y(xe),circle:"",size:"small",onClick:Q(ie=>J(),["stop"])},null,8,["icon","onClick"]),e(R,{type:"danger",icon:y(re),circle:"",size:"small",onClick:Q(ie=>X(le),["stop"])},null,8,["icon","onClick"])])]),P.uploading?($(),z("div",Ke,[e(L,{percentage:P.progress,"show-text":!1},null,8,["percentage"])])):ee("",!0)]))),128))])):ee("",!0)],34),u.value?($(),z("div",Qe,[e(L,{percentage:_.value,status:x.value},null,8,["percentage","status"]),w("p",et,D(U.value),1)])):ee("",!0)])}}},lt=ue(tt,[["__scopeId","data-v-f68f57a9"]]),at={class:"dish-form"},ot={class:"form-actions"},st={__name:"DishForm",props:{formData:{type:Object,default:()=>({})},isEdit:{type:Boolean,default:!1}},emits:["submit","cancel"],setup(m,{expose:c,emit:s}){const A=m,S=s,d=V(),i=de({name:"",category:"",description:"",ingredients:"",cookingMethod:"",images:[],isAvailable:!0}),u={name:[{required:!0,message:"请输入菜品名称",trigger:"blur"}],category:[{required:!0,message:"请选择分类",trigger:"change"}],description:[{required:!0,message:"请输入菜品描述",trigger:"blur"}]},_=["热菜","凉菜","汤品","主食","甜品"];_e(()=>A.formData,f=>{f&&Object.keys(f).length>0&&Object.assign(i,{name:f.name||"",category:f.category||"",description:f.description||"",ingredients:f.ingredients||"",cookingMethod:f.cookingMethod||"",images:f.image?[f.image]:[],isAvailable:f.isAvailable!==void 0?f.isAvailable:!0})},{immediate:!0});const x=()=>{r.success("图片上传成功")},U=f=>{r.error("图片上传失败: "+f.message)},F=()=>B(this,null,function*(){if(d.value)try{if(!(yield d.value.validate()))return;const p=oe(W({},i),{image:i.images.length>0?i.images[0]:""});delete p.images,S("submit",p)}catch(f){console.error("表单验证失败:",f)}}),G=()=>{S("cancel")};return c({resetForm:()=>{d.value&&d.value.resetFields(),Object.assign(i,{name:"",category:"",description:"",ingredients:"",cookingMethod:"",images:[],isAvailable:!0})}}),(f,p)=>{const k=v("el-input"),I=v("el-form-item"),H=v("el-option"),J=v("el-select"),X=v("el-switch"),E=v("el-button"),n=v("el-form");return $(),z("div",at,[e(n,{ref_key:"formRef",ref:d,model:i,rules:u,"label-width":"100px"},{default:l(()=>[e(I,{label:"菜品名称",prop:"name"},{default:l(()=>[e(k,{modelValue:i.name,"onUpdate:modelValue":p[0]||(p[0]=o=>i.name=o),placeholder:"请输入菜品名称"},null,8,["modelValue"])]),_:1}),e(I,{label:"分类",prop:"category"},{default:l(()=>[e(J,{modelValue:i.category,"onUpdate:modelValue":p[1]||(p[1]=o=>i.category=o),placeholder:"请选择分类",style:{width:"100%"}},{default:l(()=>[($(),z(ye,null,be(_,o=>e(H,{key:o,label:o,value:o},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(I,{label:"描述",prop:"description"},{default:l(()=>[e(k,{modelValue:i.description,"onUpdate:modelValue":p[2]||(p[2]=o=>i.description=o),type:"textarea",rows:3,placeholder:"请输入菜品描述"},null,8,["modelValue"])]),_:1}),e(I,{label:"食材",prop:"ingredients"},{default:l(()=>[e(k,{modelValue:i.ingredients,"onUpdate:modelValue":p[3]||(p[3]=o=>i.ingredients=o),type:"textarea",rows:2,placeholder:"请输入食材，用逗号分隔"},null,8,["modelValue"])]),_:1}),e(I,{label:"制作方法",prop:"cookingMethod"},{default:l(()=>[e(k,{modelValue:i.cookingMethod,"onUpdate:modelValue":p[4]||(p[4]=o=>i.cookingMethod=o),type:"textarea",rows:4,placeholder:"请输入制作方法"},null,8,["modelValue"])]),_:1}),e(I,{label:"菜品图片"},{default:l(()=>[e(lt,{modelValue:i.images,"onUpdate:modelValue":p[5]||(p[5]=o=>i.images=o),multiple:!1,"max-count":1,"upload-type":"menu","entity-id":i.id||"new",category:"food",onUploadSuccess:x,onUploadError:U},null,8,["modelValue","entity-id"])]),_:1}),e(I,{label:"是否可用"},{default:l(()=>[e(X,{modelValue:i.isAvailable,"onUpdate:modelValue":p[6]||(p[6]=o=>i.isAvailable=o),"active-text":"可用","inactive-text":"不可用"},null,8,["modelValue"])]),_:1}),w("div",ot,[e(E,{onClick:G},{default:l(()=>p[7]||(p[7]=[h("取消")])),_:1,__:[7]}),e(E,{type:"primary",onClick:F},{default:l(()=>[h(D(m.isEdit?"更新":"创建"),1)]),_:1})])]),_:1},8,["model"])])}}},nt=ue(st,[["__scopeId","data-v-e234230f"]]),it={class:"dish-management"},rt={class:"image-slot",style:{width:"60px",height:"60px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},dt={key:1,class:"image-slot",style:{width:"60px",height:"60px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},ut={key:0,class:"dish-detail"},ct={class:"detail-header"},pt={style:{width:"200px",height:"150px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},mt={key:1,class:"image-slot",style:{width:"200px",height:"150px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},gt={class:"detail-info"},ft={class:"category"},vt={class:"status"},_t={class:"detail-content"},ht={__name:"dishes",setup(m){const c=V(!1),s=V([]),A=V(!1),S=V(!1),d=V(!1),i=V(),u=V(null),_=V([]),x=de({page:1,size:10,total:0}),U=de({}),F=V({}),G=[{prop:"image",label:"图片",width:80,slot:!0},{prop:"name",label:"菜品名称",minWidth:120},{prop:"category",label:"分类",width:100,slot:!0},{prop:"description",label:"描述",minWidth:150,showOverflowTooltip:!0},{prop:"isAvailable",label:"状态",width:80,slot:!0},{prop:"createdAt",label:"创建时间",width:150,formatter:t=>ne(t.createdAt,"YYYY-MM-DD HH:mm")},{label:"操作",width:200,slot:"operation",fixed:"right"}],te=[{prop:"name",label:"菜品名称",type:"input",placeholder:"请输入菜品名称"},{prop:"category",label:"分类",type:"select",placeholder:"选择分类",options:[{label:"热菜",value:"热菜"},{label:"凉菜",value:"凉菜"},{label:"汤品",value:"汤品"},{label:"主食",value:"主食"},{label:"甜品",value:"甜品"}]},{prop:"isAvailable",label:"状态",type:"select",placeholder:"选择状态",options:[{label:"可用",value:!0},{label:"不可用",value:!1}]}],f=Ue(()=>d.value?"编辑菜品":"新增菜品"),p=t=>({热菜:"danger",凉菜:"success",汤品:"warning",主食:"primary",甜品:"info"})[t]||"primary",k=()=>B(this,null,function*(){c.value=!0;try{const t=W({page:x.page,size:x.size},U),a=yield q.getDishes(t);a.code===200?(s.value=a.data.list||[],x.total=a.data.total||0):r.error(a.message||"加载数据失败")}catch(t){console.error("加载菜品列表失败:",t),r.error("加载数据失败")}finally{c.value=!1}}),I=t=>{Object.assign(U,t),x.page=1,k()},H=()=>{Object.keys(U).forEach(t=>{delete U[t]}),x.page=1,k()},J=t=>{x.page=t,k()},X=t=>{x.size=t,x.page=1,k()},E=t=>B(this,null,function*(){t.statusLoading=!0;try{const a=yield q.updateDish(t.id,{isPublished:t.isAvailable});a.code===200?r.success("状态更新成功"):(r.error(a.message||"状态更新失败"),t.isAvailable=!t.isAvailable)}catch(a){console.error("更新状态失败:",a),r.error("状态更新失败"),t.isAvailable=!t.isAvailable}finally{t.statusLoading=!1}}),n=()=>{d.value=!1,F.value={},A.value=!0},o=t=>{u.value=t,S.value=!0},b=t=>{d.value=!0,F.value=W({},t),A.value=!0},T=t=>{d.value=!1,F.value=oe(W({},t),{id:void 0,name:`${t.name} - 副本`}),A.value=!0},R=t=>B(this,null,function*(){try{const a=yield q.deleteDish(t.id);a.code===200?(r.success("删除成功"),k()):r.error(a.message||"删除失败")}catch(a){console.error("删除菜品失败:",a),r.error("删除失败")}}),L=()=>B(this,null,function*(){if(_.value.length===0){r.warning("请选择要删除的菜品");return}try{yield Oe.confirm(`确定要删除选中的 ${_.value.length} 个菜品吗？`,"批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=_.value.map(a=>a.id);yield q.batchOperation({operation:"delete",dishIds:t}),r.success("批量删除成功"),_.value=[],k()}catch(t){t!=="cancel"&&(console.error("批量删除失败:",t),r.error("批量删除失败"))}}),P=t=>{_.value=t},le=()=>{const t=document.createElement("input");t.type="file",t.accept=".xlsx,.xls",t.onchange=a=>B(this,null,function*(){const C=a.target.files[0];if(!C)return;const M=Be(C);if(!M.valid){r.error(M.errors.join(", "));return}try{r.info("正在导入数据...");const O=yield Ne(C,{columnMapping:{菜品名称:"name",分类:"category",描述:"description",食材:"ingredients",制作方法:"cookingMethod",是否可用:"isAvailable"},validator:(j,Z)=>{const N=[];return j.name||N.push(`第${Z+1}行：菜品名称不能为空`),j.category||N.push(`第${Z+1}行：分类不能为空`),j.description||N.push(`第${Z+1}行：描述不能为空`),{valid:N.length===0,errors:N}}}),ae=O.map(j=>q.createDish(oe(W({},j),{isAvailable:j.isAvailable==="是"||j.isAvailable===!0,image:""})));yield Promise.all(ae),r.success(`成功导入 ${O.length} 条数据`),k()}catch(O){console.error("导入失败:",O),O.type==="validation"?r.error("数据验证失败，请检查Excel格式"):r.error("导入失败: "+O.message)}}),t.click()},ie=()=>{try{const t=[{prop:"name",label:"菜品名称",width:120},{prop:"category",label:"分类",width:100},{prop:"description",label:"描述",width:200},{prop:"ingredients",label:"食材",width:150},{prop:"cookingMethod",label:"制作方法",width:200},{prop:"isAvailable",label:"是否可用",width:100,formatter:a=>a?"是":"否"},{prop:"createdAt",label:"创建时间",width:150,formatter:a=>ne(a)}];Le(s.value,t,"菜品数据",{sheetName:"菜品列表"})}catch(t){console.error("导出失败:",t),r.error("导出失败: "+t.message)}},ke=()=>{try{Re([{prop:"name",label:"菜品名称",example:"宫保鸡丁"},{prop:"category",label:"分类",example:"热菜"},{prop:"description",label:"描述",example:"经典川菜，酸甜可口"},{prop:"ingredients",label:"食材",example:"鸡肉,花生米,青椒,红椒"},{prop:"cookingMethod",label:"制作方法",example:"1.鸡肉切丁腌制 2.热锅下油炒制..."},{prop:"isAvailable",label:"是否可用",example:"是"}],"菜品导入模板")}catch(t){console.error("下载模板失败:",t),r.error("下载模板失败: "+t.message)}},Ce=t=>B(this,null,function*(){try{d.value?(yield q.updateDish(F.value.id,t),r.success("更新成功")):(yield q.createDish(t),r.success("创建成功")),A.value=!1,k()}catch(a){console.error("提交失败:",a),r.error("操作失败")}}),ce=()=>{A.value=!1,F.value={},i.value&&i.value.resetForm()};return Ie(()=>{k()}),(t,a)=>{const C=v("el-icon"),M=v("el-button"),O=v("el-image"),ae=v("el-tag"),j=v("el-switch"),Z=v("el-popconfirm"),N=v("el-dialog"),K=v("el-descriptions-item"),Ve=v("el-descriptions");return $(),z("div",it,[e(je,{title:"菜品管理",data:s.value,columns:G,loading:c.value,pagination:x,"show-search":!0,"show-selection":!0,"search-fields":te,onSearch:I,onReset:H,onCurrentChange:J,onSizeChange:X,onSelectionChange:P},{toolbar:l(()=>[e(M,{type:"primary",onClick:n},{default:l(()=>[e(C,null,{default:l(()=>[e(y(he))]),_:1}),a[2]||(a[2]=h(" 新增菜品 "))]),_:1,__:[2]}),e(M,{type:"danger",disabled:_.value.length===0,onClick:L},{default:l(()=>[e(C,null,{default:l(()=>[e(y(re))]),_:1}),a[3]||(a[3]=h(" 批量删除 "))]),_:1,__:[3]},8,["disabled"]),e(M,{type:"success",onClick:le},{default:l(()=>[e(C,null,{default:l(()=>[e(y(Te))]),_:1}),a[4]||(a[4]=h(" 批量导入 "))]),_:1,__:[4]}),e(M,{type:"warning",onClick:ke},{default:l(()=>[e(C,null,{default:l(()=>[e(y(fe))]),_:1}),a[5]||(a[5]=h(" 下载模板 "))]),_:1,__:[5]}),e(M,{type:"info",onClick:ie},{default:l(()=>[e(C,null,{default:l(()=>[e(y(fe))]),_:1}),a[6]||(a[6]=h(" 导出数据 "))]),_:1,__:[6]})]),image:l(({row:g})=>[g.image&&g.image.startsWith("http")?($(),ge(O,{key:0,src:g.image,alt:g.name,style:{width:"60px",height:"60px","border-radius":"8px"},fit:"cover","preview-src-list":[g.image],"preview-teleported":!0},{error:l(()=>[w("div",rt,[e(C,{size:"24"},{default:l(()=>[e(y(se))]),_:1})])]),_:2},1032,["src","alt","preview-src-list"])):($(),z("div",dt,[e(C,{size:"24"},{default:l(()=>[e(y(se))]),_:1})]))]),category:l(({row:g})=>[e(ae,{type:p(g.category),size:"small"},{default:l(()=>[h(D(g.category),1)]),_:2},1032,["type"])]),isAvailable:l(({row:g})=>[e(j,{modelValue:g.isAvailable,"onUpdate:modelValue":Y=>g.isAvailable=Y,onChange:Y=>E(g),loading:g.statusLoading},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])]),operation:l(({row:g})=>[e(M,{size:"small",type:"primary",link:"",onClick:Y=>o(g)},{default:l(()=>[e(C,null,{default:l(()=>[e(y(xe))]),_:1}),a[7]||(a[7]=h(" 查看 "))]),_:2,__:[7]},1032,["onClick"]),e(M,{size:"small",type:"warning",link:"",onClick:Y=>b(g)},{default:l(()=>[e(C,null,{default:l(()=>[e(y(Fe))]),_:1}),a[8]||(a[8]=h(" 编辑 "))]),_:2,__:[8]},1032,["onClick"]),e(M,{size:"small",type:"success",link:"",onClick:Y=>T(g)},{default:l(()=>[e(C,null,{default:l(()=>[e(y(Ee))]),_:1}),a[9]||(a[9]=h(" 复制 "))]),_:2,__:[9]},1032,["onClick"]),e(Z,{title:"确定要删除这个菜品吗？",onConfirm:Y=>R(g)},{reference:l(()=>[e(M,{size:"small",type:"danger",link:""},{default:l(()=>[e(C,null,{default:l(()=>[e(y(re))]),_:1}),a[10]||(a[10]=h(" 删除 "))]),_:1,__:[10]})]),_:2},1032,["onConfirm"])]),_:1},8,["data","loading","pagination"]),e(N,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=g=>A.value=g),title:f.value,width:"800px","close-on-click-modal":!1,onClose:ce},{default:l(()=>[e(nt,{ref_key:"dishFormRef",ref:i,"form-data":F.value,"is-edit":d.value,onSubmit:Ce,onCancel:ce},null,8,["form-data","is-edit"])]),_:1},8,["modelValue","title"]),e(N,{modelValue:S.value,"onUpdate:modelValue":a[1]||(a[1]=g=>S.value=g),title:"菜品详情",width:"600px"},{default:l(()=>[u.value?($(),z("div",ut,[w("div",ct,[u.value.image&&u.value.image.startsWith("http")?($(),ge(O,{key:0,src:u.value.image,alt:u.value.name,style:{width:"200px",height:"150px","border-radius":"8px"},fit:"cover"},{error:l(()=>[w("div",pt,[e(C,{size:"48"},{default:l(()=>[e(y(se))]),_:1})])]),_:1},8,["src","alt"])):($(),z("div",mt,[e(C,{size:"48"},{default:l(()=>[e(y(se))]),_:1})])),w("div",gt,[w("h3",null,D(u.value.name),1),w("p",ft,"分类："+D(u.value.category),1),w("p",vt,[a[11]||(a[11]=h(" 状态： ")),e(ae,{type:u.value.isAvailable?"success":"danger",size:"small"},{default:l(()=>[h(D(u.value.isAvailable?"可用":"不可用"),1)]),_:1},8,["type"])])])]),w("div",_t,[e(Ve,{column:1,border:""},{default:l(()=>[e(K,{label:"菜品描述"},{default:l(()=>[h(D(u.value.description||"暂无描述"),1)]),_:1}),e(K,{label:"食材"},{default:l(()=>[h(D(u.value.ingredients||"暂无食材信息"),1)]),_:1}),e(K,{label:"制作方法"},{default:l(()=>[h(D(u.value.cookingMethod||"暂无制作方法"),1)]),_:1}),e(K,{label:"创建时间"},{default:l(()=>[h(D(y(ne)(u.value.createdAt)),1)]),_:1}),e(K,{label:"更新时间"},{default:l(()=>[h(D(y(ne)(u.value.updatedAt)),1)]),_:1})]),_:1})])])):ee("",!0)]),_:1},8,["modelValue"])])}}},wt=ue(ht,[["__scopeId","data-v-b9102d99"]]);export{wt as default};
