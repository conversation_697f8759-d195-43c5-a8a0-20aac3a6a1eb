# 楠楠家厨小程序项目

## 📋 项目介绍
为楠楠定制的微信小程序，包含完整的前端、后端和管理系统。

## 🏗️ 软件架构
- **前端**: 微信小程序
- **后端**: Node.js + Express + Prisma
- **管理后台**: Vue 3 + Vite + Tailwind CSS
- **数据库**: MySQL
- **服务器**: Ubuntu 24.04 LTS + Nginx + PM2

## 🌐 在线访问地址
- **🏠 管理后台**: https://www.huanglun.asia
- **🔌 API生产环境**: https://www.huanglun.asia/api/health
- **🧪 API测试环境**: https://www.huanglun.asia/api-test/health

## 📁 项目结构
```
wx-nan/
├── pages/              # 小程序页面
├── components/         # 小程序组件
├── utils/             # 工具函数
├── config/            # 配置文件
├── webs/              # Web应用
│   ├── server/        # 后端API服务
│   └── admin/         # 管理后台
├── testing/           # 测试工具
└── 架构相关/          # 架构文档
```

## 🔧 技术栈详情

### 前端小程序
- **框架**: 微信小程序原生
- **状态管理**: 本地存储 + 全局状态
- **网络请求**: wx.request 封装
- **UI组件**: 自定义组件库

### 后端服务
- **运行时**: Node.js 18.2
- **框架**: Express.js
- **ORM**: Prisma
- **数据库**: MySQL 8.4
- **认证**: JWT
- **图床**: GitHub + PicX

### 管理后台
- **框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI框架**: Tailwind CSS + Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4

### 服务器环境
- **操作系统**: Ubuntu 24.04 LTS
- **Web服务器**: Nginx (HTTPS/SSL)
- **进程管理**: PM2
- **SSL证书**: Let's Encrypt
- **域名**: www.huanglun.asia

## 📚 文档目录

### 配置指南
- [nginx配置应用指南.md](./nginx配置应用指南.md) - Nginx配置详解
- [SSL证书配置指南.md](./SSL证书配置指南.md) - HTTPS配置指南
- [自动部署优化指南.md](./自动部署优化指南.md) - 部署脚本优化
- [WebHook自动部署配置指南.md](./WebHook自动部署配置指南.md) - GitHub自动部署

### 账号信息
- [账号密码相关.md](./账号密码相关.md) - 服务器和服务账号信息

## 🚀 快速开始

### 本地开发
```bash
# 小程序开发
# 使用微信开发者工具打开项目根目录

# 后端开发
cd webs/server
npm install
npm run dev

# 管理后台开发
cd webs/admin
pnpm install
pnpm dev
```

### 生产部署
```bash
# 自动部署（推送到GitHub后自动触发）
git push origin master

# 手动部署
bash webs/server/deploy.sh
```

## 🔄 环境配置

### 开发环境
- **小程序**: 微信开发者工具
- **后端**: localhost:3000
- **管理后台**: localhost:5173

### 测试环境
- **后端**: https://www.huanglun.asia/api-test/
- **数据库**: nannan_db_test
- **PM2服务**: nannan-api-test

### 生产环境
- **后端**: https://www.huanglun.asia/api/
- **数据库**: nannan_db
- **PM2服务**: nannan-api

## 🛠️ 外部服务

### 图床服务
- **服务**: GitHub + PicX
- **文档**: https://picx-docs.xpoet.cn/usage-guide/get-start.html
- **管理**: https://picx.xpoet.cn/#/config

### 云数据库（备用）
- **服务**: Neon Tech
- **地址**: https://console.neon.tech/
- **ORM**: Prisma

### 部署平台（备用）
- **服务**: Vercel
- **地址**: https://vercel.com/

## ✅ 项目状态

- [x] 小程序前端开发完成
- [x] 后端API服务完成
- [x] 管理后台开发完成
- [x] HTTPS SSL配置完成
- [x] 自动部署配置完成
- [x] 测试/生产环境分离
- [x] 文档完善

**🎉 项目已完成并上线运行！**