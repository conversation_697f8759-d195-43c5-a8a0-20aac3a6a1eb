var L=Object.defineProperty;var S=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var C=(t,n,o)=>n in t?L(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o,V=(t,n)=>{for(var o in n||(n={}))R.call(n,o)&&C(t,o,n[o]);if(S)for(var o of S(n))W.call(n,o)&&C(t,o,n[o]);return t};import{_ as J,r as K,a as M,G as Q,c as p,m,d as b,t as X,a9 as z,e as g,w as c,N as y,O as f,f as i,k as x,l as u,aa as Y,h as l,ab as Z}from"./index-CtHojCwd.js";const ee={class:"table-container"},te={key:0,class:"table-header"},ae={class:"table-title"},le={class:"table-actions"},oe={key:1,class:"table-search"},ne={class:"table-wrapper"},se={key:2,class:"table-pagination"},re={__name:"CustomTable",props:{title:{type:String,default:""},data:{type:Array,default:()=>[]},columns:{type:Array,required:!0},showHeader:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!1},searchFields:{type:Array,default:()=>[]},showSelection:{type:Boolean,default:!1},showPagination:{type:Boolean,default:!0},pagination:{type:Object,default:()=>({page:1,size:10,total:0})},pageSizes:{type:Array,default:()=>[10,20,50,100]},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"加载中..."}},emits:["search","reset","selection-change","sort-change","size-change","current-change"],setup(t,{emit:n}){const o=t,h=n,_=K([]),s=M({}),B=()=>{o.searchFields.forEach(a=>{s[a.prop]=""})},U=()=>{h("search",V({},s))},F=()=>{Object.keys(s).forEach(a=>{s[a]=""}),h("reset")},T=a=>{h("selection-change",a)},N=a=>{h("sort-change",a)},A=a=>{h("size-change",a)},O=a=>{h("current-change",a)};return Q(()=>o.data,a=>{_.value=a},{immediate:!0}),B(),(a,d)=>{const P=i("el-input"),j=i("el-option"),E=i("el-select"),$=i("el-date-picker"),w=i("el-form-item"),k=i("el-button"),D=i("el-form"),v=i("el-table-column"),H=i("el-table"),q=i("el-pagination");return l(),p("div",ee,[t.showHeader?(l(),p("div",te,[b("div",ae,X(t.title),1),b("div",le,[z(a.$slots,"toolbar",{},void 0,!0)])])):m("",!0),t.showSearch?(l(),p("div",oe,[g(D,{model:s,inline:""},{default:c(()=>[(l(!0),p(y,null,f(t.searchFields,e=>(l(),u(w,{key:e.prop,label:e.label},{default:c(()=>[e.type==="input"?(l(),u(P,{key:0,modelValue:s[e.prop],"onUpdate:modelValue":r=>s[e.prop]=r,placeholder:e.placeholder,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(l(),u(E,{key:1,modelValue:s[e.prop],"onUpdate:modelValue":r=>s[e.prop]=r,placeholder:e.placeholder,clearable:"",style:{"min-width":"150px",width:"100%"}},{default:c(()=>[(l(!0),p(y,null,f(e.options,r=>(l(),u(j,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="date"?(l(),u($,{key:2,modelValue:s[e.prop],"onUpdate:modelValue":r=>s[e.prop]=r,type:"date",placeholder:e.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"])):m("",!0)]),_:2},1032,["label"]))),128)),g(w,null,{default:c(()=>[g(k,{type:"primary",onClick:U},{default:c(()=>d[2]||(d[2]=[x("搜索")])),_:1,__:[2]}),g(k,{onClick:F},{default:c(()=>d[3]||(d[3]=[x("重置")])),_:1,__:[3]})]),_:1})]),_:1},8,["model"])])):m("",!0),b("div",ne,[g(H,Y({data:_.value,loading:t.loading,"element-loading-text":t.loadingText,"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},a.$attrs,{onSelectionChange:T,onSortChange:N,class:"custom-table"}),{default:c(()=>[t.showSelection?(l(),u(v,{key:0,type:"selection",width:"55"})):m("",!0),(l(!0),p(y,null,f(t.columns,e=>(l(),u(v,{key:e.prop||e.label,prop:e.prop,label:e.label,width:e.width,"min-width":e.minWidth,sortable:e.sortable,formatter:e.formatter,"show-overflow-tooltip":e.showOverflowTooltip!==!1,fixed:e.fixed},Z({_:2},[e.slot?{name:"default",fn:c(({row:r,column:G,$index:I})=>[z(a.$slots,e.slot===!0?e.prop:e.slot,{row:r,column:G,index:I},void 0,!0)]),key:"0"}:void 0]),1032,["prop","label","width","min-width","sortable","formatter","show-overflow-tooltip","fixed"]))),128))]),_:3},16,["data","loading","element-loading-text"])]),t.showPagination?(l(),p("div",se,[g(q,{"current-page":t.pagination.page,"onUpdate:currentPage":d[0]||(d[0]=e=>t.pagination.page=e),"page-size":t.pagination.size,"onUpdate:pageSize":d[1]||(d[1]=e=>t.pagination.size=e),"page-sizes":t.pageSizes,total:t.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:A,onCurrentChange:O},null,8,["current-page","page-size","page-sizes","total"])])):m("",!0)])}}},ce=J(re,[["__scopeId","data-v-bca869f7"]]);export{ce as C};
