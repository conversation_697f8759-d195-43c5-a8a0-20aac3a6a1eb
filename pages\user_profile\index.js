const {userApi, uploadApi} = require('../../services/api');

Page({
  data: {
    userInfo: {},
    form: {
      name: '',
      phone: '',
      email: '',
      gender: '',
      birthday: '',
      address: '',
      bio: '',
      password: '',
      confirmPassword: ''
    },
    loading: false,
    saving: false,

    // 性别选项
    genderOptions: [
      {
        text: '男',
        value: 'male'
      },
      {
        text: '女',
        value: 'female'
      },
      {
        text: '其他',
        value: 'other'
      }
    ],
    showGenderPicker: false,

    // 原生日期选择器的最大日期（字符串格式）
    maxDateString: new Date().toISOString().split('T')[0], // 格式：YYYY-MM-DD

    // 头像上传
    showAvatarActions: false,

    // 性别显示文本
    genderDisplayText: '请选择性别',

    // 页面状态
    isRequired: false, // 是否为必填模式（从登录页面跳转过来）
    showBackButton: true, // 是否显示返回按钮
    needPasswordReset: false // 是否需要设置密码（首次完善信息时）
  },

  onLoad(options) {
    // 检查是否为必填模式
    if (options && options.required === 'true') {
      this.setData({
        isRequired: true,
        showBackButton: false,
        needPasswordReset: true // 必填模式下需要设置密码
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '完善个人信息'
      });
    }

    this.loadUserInfo();
  },

  // 页面显示时的处理
  onShow() {
    // 如果是必填模式，禁用返回手势
    if (this.data.isRequired) {
      // 注意：小程序无法完全禁用返回手势，但可以在用户尝试返回时进行拦截
    }
  },

  // 监听用户点击左上角返回按钮或返回手势
  onUnload() {
    // 如果是必填模式且用户强制返回，阻止返回并给出提示
    if (this.data.isRequired) {
      wx.showModal({
        title: '无法返回',
        content: '请完善必要的账户信息后才能继续使用',
        showCancel: false,
        confirmText: '我知道了'
      });
      return false; // 阻止返回
    }
  },

  // 监听页面返回事件
  onBackPress() {
    if (this.data.isRequired) {
      wx.showModal({
        title: '无法返回',
        content: '请完善必要的账户信息后才能继续使用',
        showCancel: false,
        confirmText: '我知道了'
      });
      return true; // 阻止返回
    }
    return false; // 允许返回
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      this.setData({
        loading: true
      });

      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        wx.showToast({
          title: '用户信息获取失败',
          icon: 'none'
        });
        return;
      }

      // 从服务器获取最新用户信息
      const res = await userApi.getUserInfo(userInfo.id);

      if (res.code === 200) {
        const userData = res.data;

        // 检查是否需要密码重置（必填模式且没有手机号）
        const needPasswordReset =
          this.data.isRequired &&
          (!userData.phone || userData.phone.trim() === '');

        this.setData({
          userInfo: userData,
          needPasswordReset,
          form: {
            name: userData.name || '',
            phone: userData.phone || '',
            email: userData.email || '',
            gender: userData.gender || '',
            birthday: userData.birthday
              ? this.formatDate(userData.birthday)
              : '',
            address: userData.address || '',
            bio: userData.bio || '',
            password: '',
            confirmPassword: ''
          },
          genderDisplayText: this.getGenderText(userData.gender || '')
        });
      } else {
        wx.showToast({
          title: res.message || '获取用户信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Load user info error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false
      });
    }
  },

  /**
   * 表单输入处理
   */
  onNameChange(event) {
    this.setData({
      'form.name': event.detail
    });
  },

  onPhoneChange(event) {
    this.setData({
      'form.phone': event.detail
    });
  },

  onEmailChange(event) {
    this.setData({
      'form.email': event.detail
    });
  },

  // 密码输入
  onPasswordChange(event) {
    this.setData({
      'form.password': event.detail
    });
  },

  // 确认密码输入
  onConfirmPasswordChange(event) {
    this.setData({
      'form.confirmPassword': event.detail
    });
  },

  onAddressChange(event) {
    this.setData({
      'form.address': event.detail
    });
  },

  onBioChange(event) {
    this.setData({
      'form.bio': event.detail
    });
  },

  /**
   * 性别选择
   */
  showGenderPicker() {
    this.setData({
      showGenderPicker: true
    });
  },

  onGenderConfirm(event) {
    const value = event.currentTarget.dataset.value;
    this.setData({
      'form.gender': value,
      genderDisplayText: this.getGenderText(value),
      showGenderPicker: false
    });
  },

  onGenderCancel() {
    this.setData({
      showGenderPicker: false
    });
  },

  /**
   * 原生生日选择器处理
   */
  onNativeDateChange(event) {
    const selectedDate = event.detail.value;
    this.setData({
      'form.birthday': selectedDate
    });
  },

  /**
   * 头像相关
   */
  showAvatarActions() {
    this.setData({
      showAvatarActions: true
    });
  },

  hideAvatarActions() {
    this.setData({
      showAvatarActions: false
    });
  },

  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      sizeType: ['compressed'], // 压缩图片
      mediaType: ['image'],
      sourceType: ['album', 'camera'], // 修正参数名
      success: res => {
        console.log('选择图片成功:', res);
        if (res.tempFiles && res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          this.uploadAvatar(tempFilePath);
        }
      },
      fail: err => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
    this.hideAvatarActions();
  },

  async uploadAvatar(filePath) {
    try {
      wx.showLoading({
        title: '上传中...'
      });

      console.log('开始上传头像:', filePath);

      // 调用头像上传API
      const uploadResult = await uploadApi.uploadAvatar(
        filePath,
        this.data.userInfo.id
      );
      console.log('头像上传结果:', uploadResult);

      if (uploadResult.code === 201) {
        const avatarUrl = uploadResult.data.url;

        // 如果有旧头像，先删除
        if (
          this.data.userInfo.avatar &&
          this.data.userInfo.avatar !== avatarUrl
        ) {
          try {
            await uploadApi.deleteImage(this.data.userInfo.avatar);
            console.log('旧头像删除成功');
          } catch (deleteError) {
            console.warn('删除旧头像失败:', deleteError);
            // 删除失败不影响新头像的设置
          }
        }

        // 更新用户信息中的头像
        const updateData = {avatar: avatarUrl};
        const updateResult = await userApi.updateUserInfo(
          this.data.userInfo.id,
          updateData
        );

        if (updateResult.code === 200) {
          // 更新本地数据
          const updatedUserInfo = {
            ...this.data.userInfo,
            avatar: avatarUrl
          };

          // 添加时间戳避免缓存问题
          const avatarUrlWithTimestamp = `${avatarUrl}?t=${Date.now()}`;

          this.setData({
            userInfo: {
              ...updatedUserInfo,
              avatar: avatarUrlWithTimestamp
            }
          });

          // 更新本地存储
          wx.setStorageSync('userInfo', updatedUserInfo);

          wx.hideLoading();
          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(updateResult.message || '更新用户信息失败');
        }
      } else {
        throw new Error(uploadResult.message || '上传失败');
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '头像上传失败',
        icon: 'none'
      });
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const {name, phone, email, password, confirmPassword} = this.data.form;
    const {isRequired, needPasswordReset} = this.data;

    // 姓名验证
    if (!name || name.trim().length < 2) {
      wx.showToast({
        title: '请输入至少2个字符的姓名',
        icon: 'none'
      });
      return false;
    }

    // 手机号验证
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    // 邮箱验证（必填模式下为必填）
    if (isRequired && (!email || email.trim() === '')) {
      wx.showToast({
        title: '请输入邮箱地址',
        icon: 'none'
      });
      return false;
    }

    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      wx.showToast({
        title: '请输入正确的邮箱格式',
        icon: 'none'
      });
      return false;
    }

    // 密码验证（仅在需要设置密码时）
    if (needPasswordReset) {
      if (!password || password.length < 6) {
        wx.showToast({
          title: '密码至少需要6位字符',
          icon: 'none'
        });
        return false;
      }

      if (password.length > 20) {
        wx.showToast({
          title: '密码不能超过20位字符',
          icon: 'none'
        });
        return false;
      }

      if (!confirmPassword || confirmPassword !== password) {
        wx.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 保存用户信息
   */
  async saveUserInfo() {
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({
        saving: true
      });

      const {userInfo, form, needPasswordReset} = this.data;
      const updateData = {
        ...form,
        birthday: form.birthday ? new Date(form.birthday).toISOString() : null
      };

      // 如果需要设置密码，添加密码字段
      if (needPasswordReset && form.password) {
        updateData.password = form.password;
        updateData.isFirstTimeSetup = true; // 标记为首次设置
      }

      // 移除确认密码字段（不需要发送到后端）
      delete updateData.confirmPassword;

      const res = await userApi.updateUserInfo(userInfo.id, updateData);

      if (res.code === 200) {
        // 更新本地存储的用户信息
        const updatedUserInfo = {
          ...userInfo,
          ...res.data
        };
        wx.setStorageSync('userInfo', updatedUserInfo);

        // 更新页面显示的用户信息和表单数据
        this.setData({
          userInfo: updatedUserInfo,
          form: {
            name: updatedUserInfo.name || '',
            phone: updatedUserInfo.phone || '',
            email: updatedUserInfo.email || '',
            gender: updatedUserInfo.gender || '',
            birthday: updatedUserInfo.birthday
              ? this.formatDate(updatedUserInfo.birthday)
              : '',
            address: updatedUserInfo.address || '',
            bio: updatedUserInfo.bio || ''
          },
          genderDisplayText: this.getGenderText(updatedUserInfo.gender || '')
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        });

        // 根据是否为必填模式决定跳转逻辑
        setTimeout(() => {
          if (this.data.isRequired) {
            // 必填模式，跳转到首页
            wx.switchTab({
              url: '/pages/home/<USER>'
            });
          } else {
            // 普通模式，返回上一页
            wx.navigateBack();
          }
        }, 2000);
      } else {
        wx.showToast({
          title: res.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Save user info error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({
        saving: false
      });
    }
  },

  /**
   * 工具方法
   */
  formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  getGenderText(gender) {
    const genderMap = {
      male: '男',
      female: '女',
      other: '其他'
    };
    return genderMap[gender] || '未设置';
  }
});
