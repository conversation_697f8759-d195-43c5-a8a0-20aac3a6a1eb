import{K as n}from"./index-CtHojCwd.js";function i(t,o=2){if(t===0)return"0 Bytes";const r=1024,f=o<0?0:o,s=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],a=Math.floor(Math.log(t)/Math.log(r));return parseFloat((t/Math.pow(r,a)).toFixed(f))+" "+s[a]}function B(t,o="YYYY-MM-DD HH:mm:ss"){return t?n(t).format(o):""}function m(t,o="YYYY-MM-DD"){return t?n(t).format(o):""}export{B as a,m as b,i as f};
