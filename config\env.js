/**
 * 环境配置文件
 * 统一管理所有环境相关的配置
 * 支持通过npm scripts动态切换环境
 */

// 尝试加载动态生成的环境配置
let dynamicConfig = null;
try {
  dynamicConfig = require('./env.generated.js');
  console.log(`🎯 使用动态环境配置: ${dynamicConfig.currentEnv}`);
} catch (e) {
  console.log('📋 使用默认环境配置');
}

// 环境配置
const ENV_CONFIG = {
  // 开发环境
  development: {
    // API配置
    // baseURL: 'http://localhost:3000/api',  // 本地开发服务器
    baseURL: 'https://www.huanglun.asia/api', // 线上测试服务器
    timeout: 10000,

    // 微信小程序配置
    wechatAppId: 'wx82283b353918af82', // 请替换为您的小程序AppID

    // 调试配置
    debug: true,
    enableMock: false,
    logLevel: 'debug',

    // 其他配置
    enableCache: true,
    cacheTimeout: 5 * 60 * 1000 // 5分钟
  },

  // 生产环境
  production: {
    // API配置
    baseURL: 'https://www.huanglun.asia/api', // 生产环境API地址
    timeout: 15000,

    // 微信小程序配置
    wechatAppId: 'wx82283b353918af82', // 生产环境小程序AppID

    // 调试配置
    debug: false,
    enableMock: false,
    logLevel: 'error',

    // 其他配置
    enableCache: true,
    cacheTimeout: 10 * 60 * 1000 // 10分钟
  }
};

/**
 * 获取当前环境
 * 优先级：动态配置 > 小程序环境判断 > 默认配置
 */
function getCurrentEnv() {
  // 方法1：如果有动态配置，优先使用
  if (dynamicConfig && dynamicConfig.currentEnv) {
    return dynamicConfig.currentEnv;
  }

  // 方法2：通过小程序环境自动判断
  try {
    const accountInfo = wx.getAccountInfoSync();
    // 开发版和体验版使用development，正式版使用production
    return accountInfo.miniProgram.envVersion === 'release'
      ? 'production'
      : 'development';
  } catch (e) {
    // 方法3：默认环境
    return 'development';
  }
}

/**
 * API服务器配置
 * 根据环境自动选择对应的API地址
 */
const API_SERVERS = {
  development: 'http://localhost:3000/api', // 本地开发服务器
  test: 'https://www.huanglun.asia/api-test', // 线上测试环境 (测试数据库)
  production: 'https://www.huanglun.asia/api' // 线上生产环境 (正式数据库)
};

// 获取当前环境配置
const currentEnv = getCurrentEnv();

// 如果有动态配置，优先使用动态配置
const config = dynamicConfig
  ? {
      ...dynamicConfig,
      // 确保使用正确的API地址
      baseURL:
        dynamicConfig.baseURL ||
        API_SERVERS[currentEnv] ||
        ENV_CONFIG[currentEnv].baseURL
    }
  : {
      ...ENV_CONFIG[currentEnv],
      // 根据环境自动选择API地址
      baseURL: API_SERVERS[currentEnv] || ENV_CONFIG[currentEnv].baseURL
    };

// 输出当前环境信息（仅开发环境）
if (config.debug) {
  console.log(`🌍 当前环境: ${currentEnv}`);
  console.log(`🔗 API地址: ${config.baseURL}`);
  if (dynamicConfig) {
    console.log(
      `📝 环境描述: ${dynamicConfig.envDescription || currentEnv + '环境'}`
    );
    console.log(`⚙️ 配置来源: 动态生成`);
  } else {
    console.log(`⚙️ 配置来源: 默认配置`);
  }
  console.log(`📱 小程序AppID: ${config.wechatAppId}`);
}

// API路径配置
const API_PATHS = {
  // 认证相关
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout'
  },

  // 用户相关
  users: {
    profile: '/users/profile',
    update: '/users',
    family: '/users/family',
    connected: '/users/connected'
  },

  // 菜单相关
  menus: {
    today: '/menus/today',
    history: '/menus/history',
    recommended: '/menus/recommended',
    statistics: '/menus/statistics',
    create: '/menus',
    categories: '/menus/categories'
  },

  // 菜品相关
  dishes: {
    list: '/dishes',
    byCategory: '/dishes/by-category',
    categories: '/dishes/categories',
    categoriesForMiniProgram: '/dishes/categories/miniprogram',
    my: '/dishes/my',
    detail: '/dishes',
    statistics: '/dishes/statistics'
  },

  // 订单相关
  orders: {
    list: '/orders',
    today: '/orders/today',
    create: '/orders',
    createAndPush: '/order-push/create-and-push',
    visible: '/order-push/visible'
  },

  // 消息相关
  messages: {
    list: '/messages',
    create: '/messages'
  },

  // 通知相关
  notifications: {
    list: '/notifications',
    create: '/notifications',
    markRead: '/notifications',
    markAllRead: '/notifications/read-all'
  },

  // 用户关联相关
  connections: {
    users: '/connections/users',
    request: '/connections/request',
    my: '/connections/my',
    history: '/connections/history',
    statistics: '/connections/statistics'
  },

  // 订阅通知相关
  subscriptions: {
    orderNotification: '/subscriptions/order-notification',
    menuNotification: '/subscriptions/menu-notification',
    test: '/subscriptions/test'
  },

  // 文件上传相关
  upload: {
    image: '/upload/image'
  }
};

/**
 * 获取完整的API URL
 * @param {string} path - API路径，支持多种格式：
 *   - 完整路径：'/api/users/profile'
 *   - 相对路径：'users.profile'
 *   - 直接路径：'profile'
 * @returns {string} 完整的API URL
 */
function getApiUrl(path) {
  // 如果是完整路径（以/开头）
  if (path.startsWith('/')) {
    return config.baseURL + path;
  }

  // 如果是相对路径（包含.）
  if (path.includes('.')) {
    const pathParts = path.split('.');
    let apiPath = API_PATHS;

    for (const part of pathParts) {
      if (apiPath && apiPath[part]) {
        apiPath = apiPath[part];
      } else {
        console.warn(`API路径不存在: ${path}`);
        return config.baseURL + '/' + path;
      }
    }

    return config.baseURL + apiPath;
  }

  // 直接返回拼接结果
  return config.baseURL + '/' + path;
}

/**
 * 获取带参数的API URL
 * @param {string} path - API路径
 * @param {object} params - 查询参数
 * @returns {string} 带参数的完整URL
 */
function getApiUrlWithParams(path, params = {}) {
  const baseUrl = getApiUrl(path);

  const queryString =
    Object.keys(params).length > 0
      ? '?' +
        Object.keys(params)
          .filter(key => params[key] !== undefined && params[key] !== null)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&')
      : '';

  return baseUrl + queryString;
}

// 导出配置
module.exports = {
  // 当前环境配置
  ...config,

  // 环境信息
  currentEnv,

  // API路径配置
  API_PATHS,

  // 工具方法
  getApiUrl,
  getApiUrlWithParams,

  // 快捷访问
  baseURL: config.baseURL,
  wechatAppId: config.wechatAppId,
  debug: config.debug
};
