var M=(N,_,o)=>new Promise((k,Y)=>{var x=u=>{try{h(o.next(u))}catch(v){Y(v)}},z=u=>{try{h(o.throw(u))}catch(v){Y(v)}},h=u=>u.done?k(u.value):Promise.resolve(u.value).then(x,z);h((o=o.apply(N,_)).next())});import{_ as O,r as b,K as B,q as f,o as P,c,d as e,e as a,w as i,f as m,t as l,l as W,m as A,N as C,O as V,h as r,i as y,a3 as X,k as w,U as G,X as U,$ as H,E as p,z as $}from"./index-CtHojCwd.js";import{m as E}from"./menu-CzZqR-71.js";const J={class:"today-menu"},Q={class:"page-header"},Z={class:"header-content"},ee={class:"header-info"},se={class:"date-info"},te={class:"header-stats"},ae={class:"stat-card"},oe={class:"stat-number"},le={class:"stat-card"},ne={class:"stat-number"},ie={class:"stat-card"},re={class:"stat-number"},de={class:"action-bar"},ce={class:"action-left"},ue={class:"action-right"},_e={class:"menu-display"},ve={key:0,class:"empty-menu"},me={key:1,class:"menu-content"},pe={class:"category-header"},he={class:"category-title"},ge={class:"category-count"},fe={class:"dishes-grid"},ye={class:"dish-image"},we={class:"image-slot"},ke={class:"dish-info"},Ye={class:"dish-name"},De={key:0,class:"dish-description"},Me={class:"dish-meta"},be={class:"dish-price"},Ce={class:"menu-preview"},Ve={class:"preview-header"},xe={class:"preview-categories"},ze={class:"category-title"},Be={class:"category-dishes"},Ee={class:"preview-dish-info"},Ne={__name:"today",setup(N){const _=b(B().format("YYYY-MM-DD")),o=b([]);b(!1);const k=b(!1),Y=f(()=>B().format("YYYY年MM月DD日 dddd")),x=f(()=>o.value.reduce((t,s)=>t+(parseFloat(s.price)||0),0)),z=f(()=>new Set(o.value.map(s=>s.category)).size),h=f(()=>_.value===B().format("YYYY-MM-DD")),u=f(()=>{const t=new Map;return o.value.forEach(s=>{t.has(s.category)||t.set(s.category,{name:s.category,dishes:[]}),t.get(s.category).dishes.push(s)}),Array.from(t.values())}),v=()=>M(this,null,function*(){try{const t=yield E.getTodayMenu(_.value);t.code===200&&t.data&&t.data.dishes?o.value=t.data.dishes:o.value=[]}catch(t){console.error("加载今日菜单失败:",t),o.value=[]}}),R=t=>M(this,null,function*(){try{yield $.confirm(`确定要从今日菜单中移除"${t.name}"吗？`,"确认移除",{type:"warning"});const s=yield E.removeDishFromMenu(_.value,t.id);s.code===200?(p.success("移除成功"),v()):p.error(s.message||"移除失败")}catch(s){s!=="cancel"&&(console.error("移除菜品失败:",s),p.error("移除失败"))}}),S=t=>{v()},T=()=>{v()},j=()=>M(this,null,function*(){if(o.value.length===0){p.warning("当前没有菜单可清空");return}try{yield $.confirm("确定要清空今日菜单吗？此操作不可恢复！","确认清空",{type:"warning"});const t=yield E.clearTodayMenu(_.value);t.code===200?(p.success("菜单已清空"),v()):p.error(t.message||"清空失败")}catch(t){t!=="cancel"&&(console.error("清空菜单失败:",t),p.error("清空失败"))}});return P(()=>{v()}),(t,s)=>{const g=m("el-icon"),q=m("el-date-picker"),I=m("el-tag"),D=m("el-button"),K=m("el-empty"),F=m("el-image"),L=m("el-dialog");return r(),c("div",J,[e("div",Q,[e("div",Z,[e("div",ee,[s[2]||(s[2]=e("h1",{class:"page-title"},"今日菜单",-1)),s[3]||(s[3]=e("p",{class:"page-subtitle"},"查看和管理小程序用户看到的今日菜单",-1)),e("div",se,[a(g,null,{default:i(()=>[a(y(X))]),_:1}),e("span",null,l(Y.value),1)])]),e("div",te,[e("div",ae,[e("div",oe,l(o.value.length),1),s[4]||(s[4]=e("div",{class:"stat-label"},"菜品数量",-1))]),e("div",le,[e("div",ne,l(x.value.toFixed(2)),1),s[5]||(s[5]=e("div",{class:"stat-label"},"总价值(¥)",-1))]),e("div",ie,[e("div",re,l(z.value),1),s[6]||(s[6]=e("div",{class:"stat-label"},"涵盖分类",-1))])])])]),e("div",de,[e("div",ce,[a(q,{modelValue:_.value,"onUpdate:modelValue":s[0]||(s[0]=d=>_.value=d),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:S,size:"large"},null,8,["modelValue"]),h.value?(r(),W(I,{key:0,type:"success",class:"today-tag"},{default:i(()=>s[7]||(s[7]=[w("今天")])),_:1,__:[7]})):A("",!0)]),e("div",ue,[a(D,{onClick:T,size:"large"},{default:i(()=>[a(g,null,{default:i(()=>[a(y(G))]),_:1}),s[8]||(s[8]=w(" 刷新 "))]),_:1,__:[8]}),a(D,{onClick:j,type:"danger",plain:"",size:"large",disabled:o.value.length===0},{default:i(()=>[a(g,null,{default:i(()=>[a(y(U))]),_:1}),s[9]||(s[9]=w(" 清空菜单 "))]),_:1,__:[9]},8,["disabled"])])]),e("div",_e,[o.value.length===0?(r(),c("div",ve,[a(K,{description:"今日暂无菜单"},{default:i(()=>[a(D,{type:"primary",onClick:T},{default:i(()=>s[10]||(s[10]=[w("刷新数据")])),_:1,__:[10]})]),_:1})])):(r(),c("div",me,[(r(!0),c(C,null,V(u.value,d=>(r(),c("div",{key:d.name,class:"category-section"},[e("div",pe,[e("h3",he,l(d.name),1),e("span",ge,l(d.dishes.length)+"道菜",1)]),e("div",fe,[(r(!0),c(C,null,V(d.dishes,n=>(r(),c("div",{key:n.id,class:"dish-card"},[e("div",ye,[a(F,{src:n.image,alt:n.name,fit:"cover"},{error:i(()=>[e("div",we,[a(g,null,{default:i(()=>[a(y(H))]),_:1})])]),_:2},1032,["src","alt"])]),e("div",ke,[e("h4",Ye,l(n.name),1),n.description?(r(),c("p",De,l(n.description),1)):A("",!0),e("div",Me,[e("span",be,"¥"+l(n.price||"0"),1),a(D,{type:"danger",size:"small",plain:"",onClick:Te=>R(n)},{default:i(()=>[a(g,null,{default:i(()=>[a(y(U))]),_:1}),s[11]||(s[11]=w(" 移除 "))]),_:2,__:[11]},1032,["onClick"])])])]))),128))])]))),128))]))]),a(L,{modelValue:k.value,"onUpdate:modelValue":s[1]||(s[1]=d=>k.value=d),title:"菜单预览",width:"800px"},{default:i(()=>[e("div",Ce,[e("div",Ve,[e("h2",null,l(_.value)+" 菜单",1),e("p",null,"共 "+l(t.selectedDishes.length)+" 道菜",1)]),e("div",xe,[(r(!0),c(C,null,V(u.value,d=>(r(),c("div",{key:d.key,class:"preview-category"},[e("h3",ze,l(d.name),1),e("div",Be,[(r(!0),c(C,null,V(d.dishes,n=>(r(),c("div",{key:n.id,class:"preview-dish"},[a(F,{src:n.image,class:"preview-dish-image",fit:"cover"},null,8,["src"]),e("div",Ee,[e("h4",null,l(n.name),1),e("p",null,"¥"+l(n.price),1)])]))),128))])]))),128))])])]),_:1},8,["modelValue"])])}}},$e=O(Ne,[["__scopeId","data-v-8c012fb7"]]);export{$e as default};
