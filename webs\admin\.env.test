# 测试环境配置
NODE_ENV=test

# 应用配置
VITE_APP_TITLE=楠楠家厨管理系统(测试)
VITE_APP_VERSION=1.0.0
VITE_PORT=8848
VITE_PUBLIC_PATH=/

# 路由配置
VITE_ROUTER_HISTORY=hash

# API配置 - 使用线上测试服务器
VITE_API_BASE_URL=https://www.huanglun.asia/api-test
VITE_API_TIMEOUT=15000

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=info

# 微信配置
VITE_WECHAT_APP_ID=wx82283b353918af82

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_CACHE=true
VITE_CACHE_TIMEOUT=600000

# 其他配置
VITE_ENABLE_DEVTOOLS=true
VITE_SHOW_WATERMARK=true
