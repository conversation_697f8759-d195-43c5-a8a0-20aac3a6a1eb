var we=Object.defineProperty;var L=Object.getOwnPropertySymbols;var Ve=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable;var W=(n,r,s)=>r in n?we(n,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[r]=s,S=(n,r)=>{for(var s in r||(r={}))Ve.call(r,s)&&W(n,s,r[s]);if(L)for(var s of L(r))ke.call(r,s)&&W(n,s,r[s]);return n};var k=(n,r,s)=>new Promise((w,m)=>{var f=p=>{try{V(s.next(p))}catch(u){m(u)}},T=p=>{try{V(s.throw(p))}catch(u){m(u)}},V=p=>p.done?w(p.value):Promise.resolve(p.value).then(f,T);V((s=s.apply(n,r)).next())});import{a4 as b,_ as Ce,r as y,a as O,q as xe,o as Ne,c as z,e as a,w as o,f as d,E as c,h as C,k as g,t as H,i as K,H as Te,X as Ue,l as j,m as P,N as E,O as R,z as A,K as De}from"./index-CtHojCwd.js";import{C as ze}from"./CustomTable-C1GDYDsI.js";import{u as Ye}from"./user-C9pqxfjH.js";const x={getNotifications:n=>b.get("/notifications",n),getNotificationDetail:n=>b.get(`/notifications/${n}`),createNotification:n=>b.post("/notifications",n),updateNotification:(n,r)=>b.put(`/notifications/${n}`,r),deleteNotification:n=>b.delete(`/notifications/${n}`),publishNotification:n=>b.put(`/notifications/${n}/publish`),unpublishNotification:n=>b.put(`/notifications/${n}/unpublish`),batchDelete:n=>b.delete("/notifications/batch",{data:{ids:n}}),getNotificationStatistics:()=>b.get("/notifications/statistics")},$e={class:"notification-management"},Be={__name:"notifications",setup(n){const r=y(!1),s=y([]),w=y([]),m=y(!1),f=y("add"),T=y(!1),V=y(),p=y([]),u=O({page:1,size:10,total:0}),U=O({title:"",type:"",read:""}),i=O({id:null,title:"",content:"",type:"general",targetUserId:null,targetUsers:[]}),q=[{label:"通用通知",value:"general"},{label:"系统通知",value:"system"},{label:"菜单推送",value:"menu_push"},{label:"新菜品",value:"new_dish"},{label:"新订单",value:"new_order"},{label:"家庭消息",value:"family_message"}],X=[{label:"低",value:"low"},{label:"普通",value:"normal"},{label:"高",value:"high"},{label:"紧急",value:"urgent"}],G=[{prop:"title",label:"标题",minWidth:200,showOverflowTooltip:!0},{prop:"type",label:"类型",width:120,slot:!0},{prop:"content",label:"内容",minWidth:200,showOverflowTooltip:!0},{prop:"sender",label:"发送者",width:120,formatter:t=>{var e;return((e=t.sender)==null?void 0:e.name)||"系统"}},{prop:"read",label:"状态",width:100,slot:!0},{prop:"createdAt",label:"创建时间",width:160,formatter:t=>te(t.createdAt)},{label:"操作",width:180,slot:"operation",fixed:"right"}],J=[{prop:"title",label:"标题",type:"input",placeholder:"请输入通知标题"},{prop:"type",label:"类型",type:"select",options:q,placeholder:"请选择类型"},{prop:"read",label:"状态",type:"select",options:[{label:"未读",value:!1},{label:"已读",value:!0}],placeholder:"请选择状态"}],Q={title:[{required:!0,message:"请输入通知标题",trigger:"blur"}],content:[{required:!0,message:"请输入通知内容",trigger:"blur"}],type:[{required:!0,message:"请选择通知类型",trigger:"change"}]},Z=xe(()=>({add:"新增通知",edit:"编辑通知",view:"查看通知"})[f.value]),_=()=>k(this,null,function*(){var t;r.value=!0;try{const e=S({page:u.page,size:u.size},U),N=yield x.getNotifications(e);N.data&&(s.value=N.data.notifications||[],u.total=((t=N.data.pagination)==null?void 0:t.total)||0)}catch(e){console.error("加载通知数据失败:",e),c.error("加载数据失败"),s.value=[],u.total=0}finally{r.value=!1}}),ee=()=>k(this,null,function*(){try{const t=yield Ye.getFamilyMembers();t.data&&(p.value=t.data||[])}catch(t){console.error("加载关联用户数据失败:",t),p.value=[{id:1,name:"张三"},{id:2,name:"李四"},{id:3,name:"王五"}]}}),te=t=>t?De(t).format("YYYY-MM-DD HH:mm"):"-",le=t=>({system:"info",menu:"success",activity:"warning",maintenance:"danger",other:""})[t]||"info",ae=t=>({general:"通用通知",system:"系统通知",menu_push:"菜单推送",new_dish:"新菜品",new_order:"新订单",family_message:"家庭消息",info:"信息"})[t]||t,oe=t=>{Object.assign(U,t),u.page=1,_()},ne=()=>{Object.keys(U).forEach(t=>{U[t]=""}),u.page=1,_()},ie=t=>{u.page=t,_()},se=t=>{u.size=t,u.page=1,_()},re=t=>{w.value=t},ue=()=>{Object.assign(i,{id:null,title:"",content:"",type:"general",targetUserId:null})},de=()=>{f.value="add",ue(),m.value=!0},ce=t=>{f.value="view",Object.assign(i,t),m.value=!0},pe=t=>{f.value="edit",Object.assign(i,t),m.value=!0},me=t=>k(this,null,function*(){try{const e=t.status==="published"?"撤回":"发布";yield A.confirm(`确定要${e}这条通知吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),t.status==="published"?(yield x.unpublishNotification(t.id),c.success("通知已撤回")):(yield x.publishNotification(t.id),c.success("通知已发布")),_()}catch(e){e!=="cancel"&&(console.error("操作失败:",e),c.error("操作失败"))}}),fe=t=>k(this,null,function*(){try{yield A.confirm("确定要删除这条通知吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield x.deleteNotification(t.id),c.success("删除成功"),_()}catch(e){e!=="cancel"&&(console.error("删除通知失败:",e),c.error("删除失败"))}}),ge=()=>k(this,null,function*(){if(!w.value.length){c.warning("请选择要删除的通知");return}try{yield A.confirm(`确定要删除选中的 ${w.value.length} 条通知吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=w.value.map(e=>e.id);yield x.batchDelete(t),c.success("批量删除成功"),_()}catch(t){t!=="cancel"&&(console.error("批量删除失败:",t),c.error("批量删除失败"))}}),_e=()=>k(this,null,function*(){if(V.value)try{yield V.value.validate(),T.value=!0;const t=S({},i);f.value==="add"?(yield x.createNotification(t),c.success("新增成功")):f.value==="edit"&&(yield x.updateNotification(i.id,t),c.success("更新成功")),m.value=!1,_()}catch(t){console.error("提交失败:",t),c.error("操作失败")}finally{T.value=!1}});return Ne(()=>{_(),ee()}),(t,e)=>{const N=d("el-icon"),v=d("el-button"),F=d("el-tag"),Y=d("el-input"),h=d("el-form-item"),D=d("el-col"),$=d("el-option"),B=d("el-select"),I=d("el-row"),be=d("el-date-picker"),ve=d("el-switch"),he=d("el-form"),ye=d("el-dialog");return C(),z("div",$e,[a(ze,{title:"消息通知管理",data:s.value,columns:G,loading:r.value,pagination:u,"show-search":!0,"show-selection":!0,"search-fields":J,onSearch:oe,onReset:ne,onCurrentChange:ie,onSizeChange:se,onSelectionChange:re},{toolbar:o(()=>[a(v,{type:"primary",onClick:de},{default:o(()=>[a(N,null,{default:o(()=>[a(K(Te))]),_:1}),e[10]||(e[10]=g(" 新增消息 "))]),_:1,__:[10]}),a(v,{onClick:ge,disabled:!w.value.length},{default:o(()=>[a(N,null,{default:o(()=>[a(K(Ue))]),_:1}),e[11]||(e[11]=g(" 批量删除 "))]),_:1,__:[11]},8,["disabled"])]),type:o(({row:l})=>[a(F,{type:le(l.type),size:"small"},{default:o(()=>[g(H(ae(l.type)),1)]),_:2},1032,["type"])]),read:o(({row:l})=>[a(F,{type:l.read?"success":"warning",size:"small"},{default:o(()=>[g(H(l.read?"已读":"未读"),1)]),_:2},1032,["type"])]),operation:o(({row:l})=>[a(v,{size:"small",onClick:M=>ce(l)},{default:o(()=>e[12]||(e[12]=[g("查看")])),_:2,__:[12]},1032,["onClick"]),a(v,{size:"small",type:"primary",onClick:M=>pe(l)},{default:o(()=>e[13]||(e[13]=[g("编辑")])),_:2,__:[13]},1032,["onClick"]),a(v,{size:"small",type:l.status==="published"?"warning":"success",onClick:M=>me(l)},{default:o(()=>[g(H(l.status==="published"?"撤回":"发布"),1)]),_:2},1032,["type","onClick"]),a(v,{size:"small",type:"danger",onClick:M=>fe(l)},{default:o(()=>e[14]||(e[14]=[g("删除")])),_:2,__:[14]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),a(ye,{modelValue:m.value,"onUpdate:modelValue":e[9]||(e[9]=l=>m.value=l),title:Z.value,width:"700px","close-on-click-modal":!1},{footer:o(()=>[a(v,{onClick:e[8]||(e[8]=l=>m.value=!1)},{default:o(()=>e[15]||(e[15]=[g("取消")])),_:1,__:[15]}),f.value!=="view"?(C(),j(v,{key:0,type:"primary",loading:T.value,onClick:_e},{default:o(()=>e[16]||(e[16]=[g(" 确定 ")])),_:1,__:[16]},8,["loading"])):P("",!0)]),default:o(()=>[a(he,{ref_key:"formRef",ref:V,model:i,rules:Q,"label-width":"100px",class:"notification-form"},{default:o(()=>[a(I,{gutter:20},{default:o(()=>[a(D,{span:12},{default:o(()=>[a(h,{label:"通知标题",prop:"title"},{default:o(()=>[a(Y,{modelValue:i.title,"onUpdate:modelValue":e[0]||(e[0]=l=>i.title=l),placeholder:"请输入通知标题"},null,8,["modelValue"])]),_:1})]),_:1}),a(D,{span:12},{default:o(()=>[a(h,{label:"通知类型",prop:"type"},{default:o(()=>[a(B,{modelValue:i.type,"onUpdate:modelValue":e[1]||(e[1]=l=>i.type=l),placeholder:"请选择类型",style:{width:"100%"}},{default:o(()=>[(C(),z(E,null,R(q,l=>a($,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(I,{gutter:20},{default:o(()=>[a(D,{span:12},{default:o(()=>[a(h,{label:"优先级",prop:"priority"},{default:o(()=>[a(B,{modelValue:i.priority,"onUpdate:modelValue":e[2]||(e[2]=l=>i.priority=l),placeholder:"请选择优先级",style:{width:"100%"}},{default:o(()=>[(C(),z(E,null,R(X,l=>a($,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(D,{span:12},{default:o(()=>[a(h,{label:"发布时间",prop:"publishTime"},{default:o(()=>[a(be,{modelValue:i.publishTime,"onUpdate:modelValue":e[3]||(e[3]=l=>i.publishTime=l),type:"datetime",placeholder:"选择发布时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(h,{label:"通知内容",prop:"content"},{default:o(()=>[a(Y,{modelValue:i.content,"onUpdate:modelValue":e[4]||(e[4]=l=>i.content=l),type:"textarea",rows:6,placeholder:"请输入通知内容",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(h,{label:"目标用户",prop:"targetUsers"},{default:o(()=>[a(B,{modelValue:i.targetUsers,"onUpdate:modelValue":e[5]||(e[5]=l=>i.targetUsers=l),multiple:"",placeholder:"选择目标用户（不选则发送给所有关联用户）",style:{width:"100%"},clearable:""},{default:o(()=>[(C(!0),z(E,null,R(p.value,l=>(C(),j($,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(h,{label:"附件链接",prop:"attachmentUrl"},{default:o(()=>[a(Y,{modelValue:i.attachmentUrl,"onUpdate:modelValue":e[6]||(e[6]=l=>i.attachmentUrl=l),placeholder:"可选：相关链接或附件地址"},null,8,["modelValue"])]),_:1}),f.value==="add"?(C(),j(h,{key:0,label:"立即发布",prop:"publishNow"},{default:o(()=>[a(ve,{modelValue:i.publishNow,"onUpdate:modelValue":e[7]||(e[7]=l=>i.publishNow=l),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})):P("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},je=Ce(Be,[["__scopeId","data-v-188efa35"]]);export{je as default};
