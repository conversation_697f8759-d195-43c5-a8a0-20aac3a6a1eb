# 小程序开发指南

## 📱 开发流程

### 1. 安装依赖
```bash
npm install
```

### 2. 在微信开发者工具中构建npm
1. 打开微信开发者工具
2. 导入项目（选择项目根目录）
3. 点击菜单：**工具** → **构建npm**
4. 等待构建完成

### 3. 开始开发
- 直接在微信开发者工具中进行开发
- 修改代码后会自动刷新
- 无需执行任何命令行操作

## 🌍 环境配置

小程序会根据版本自动选择环境：

| 小程序版本 | 环境 | API地址 | 数据库 |
|------------|------|---------|--------|
| **开发版** | development | `http://localhost:3000/api` | 本地数据库 |
| **体验版** | development | `http://localhost:3000/api` | 本地数据库 |
| **正式版** | production | `https://www.huanglun.asia/api` | 正式数据库 |

### 环境说明
- **开发版/体验版**：用于开发和测试，连接本地API服务器
- **正式版**：用于生产环境，连接线上API服务器

## 📦 项目结构

```
wx-nan/
├── pages/              # 页面文件
├── components/         # 组件文件
├── utils/             # 工具函数
├── config/            # 配置文件
│   └── env.js         # 环境配置
├── app.js             # 小程序入口
├── app.json           # 小程序配置
├── app.wxss           # 全局样式
├── package.json       # 依赖配置
└── project.config.json # 项目配置
```

## 🔧 配置文件

### config/env.js
自动环境检测和API配置：

```javascript
// 环境配置
const ENV_CONFIG = {
  development: {
    baseURL: 'http://localhost:3000/api',
    debug: true,
    // ...其他配置
  },
  production: {
    baseURL: 'https://www.huanglun.asia/api',
    debug: false,
    // ...其他配置
  }
};

// 自动检测环境
function getCurrentEnv() {
  try {
    const accountInfo = wx.getAccountInfoSync();
    return accountInfo.miniProgram.envVersion === 'release'
      ? 'production'
      : 'development';
  } catch (e) {
    return 'development';
  }
}
```

### package.json
只包含必要的依赖：

```json
{
  "dependencies": {
    "@vant/weapp": "^1.11.6",
    "dayjs": "^1.11.11",
    "miniprogram-api-promise": "^1.0.4",
    "mobx-miniprogram": "^6.12.3",
    "mobx-miniprogram-bindings": "^3.0.0"
  }
}
```

## 🚀 发布流程

### 1. 预览
1. 在微信开发者工具中点击 **预览**
2. 扫码在手机上测试
3. 使用开发环境配置

### 2. 上传
1. 在微信开发者工具中点击 **上传**
2. 填写版本号和项目备注
3. 上传到微信公众平台

### 3. 发布
1. 登录微信公众平台
2. 进入版本管理
3. 提交审核 → 发布

## 📝 开发注意事项

### 1. npm包管理
- 只需要在首次使用或添加新依赖时构建npm
- 日常开发不需要重复构建
- 构建操作在微信开发者工具中完成

### 2. 环境切换
- 无需手动切换环境
- 小程序会根据版本自动选择对应环境
- 开发时使用本地API，发布后使用线上API

### 3. 调试信息
开发版会在控制台显示环境信息：
```
🌍 当前环境: development
🔗 API地址: http://localhost:3000/api
📱 小程序AppID: wx82283b353918af82
```

### 4. API调用
使用统一的API配置：
```javascript
const config = require('./config/env.js');

// 获取API地址
const apiUrl = config.getApiUrl('users.profile');
// 结果: http://localhost:3000/api/users/profile (开发环境)
//      https://www.huanglun.asia/api/users/profile (生产环境)
```

## 🛠️ 常见问题

### Q: 如何添加新的npm依赖？
A: 
1. 运行 `npm install 包名`
2. 在微信开发者工具中重新构建npm

### Q: 如何切换到测试环境？
A: 小程序没有测试环境，只有开发环境和生产环境。如需测试，使用预览功能。

### Q: 为什么API请求失败？
A: 
1. 检查本地API服务器是否启动（开发环境）
2. 检查网络连接（生产环境）
3. 查看控制台的环境信息是否正确

### Q: 如何查看当前环境？
A: 在小程序控制台查看输出的环境信息，或者调用：
```javascript
const config = require('./config/env.js');
console.log('当前环境:', config.currentEnv);
```

## ✅ 最佳实践

1. **保持简单** - 不要过度配置，使用默认的环境检测
2. **专注开发** - 在微信开发者工具中完成所有操作
3. **及时测试** - 使用预览功能在真机上测试
4. **版本管理** - 每次发布都要更新版本号

**🎉 现在可以专注于小程序开发，无需关心复杂的构建配置！**
