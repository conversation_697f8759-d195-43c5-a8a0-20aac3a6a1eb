var E=(V,y,n)=>new Promise((u,w)=>{var i=d=>{try{o(n.next(d))}catch(g){w(g)}},m=d=>{try{o(n.throw(d))}catch(g){w(g)}},o=d=>d.done?u(d.value):Promise.resolve(d.value).then(i,m);o((n=n.apply(V,y)).next())});import{_ as I,r as _,a as L,s as T,c as A,d as f,e as a,w as l,f as v,b as M,h as R,k as C,t as b,l as z,m as D,i as S,A as W,y as P,E as p}from"./index-CtHojCwd.js";const j={class:"forgot-password-container"},q={class:"forgot-password-form"},G={class:"code-input-group"},H={class:"form-footer"},J={__name:"forgot-password",setup(V){const y=M(),n=_(),u=_(!1),w=_(!1),i=_(0);let m=null;const o=L({email:"",emailCode:"",newPassword:"",confirmPassword:""}),k={email:[{validator:(s,e,r)=>{if(!e){r(new Error("请输入邮箱地址"));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)){r(new Error("请输入正确的邮箱地址"));return}r()},trigger:"blur"}],emailCode:[{validator:(s,e,r)=>{if(!e){r(new Error("请输入验证码"));return}if(e.length!==6){r(new Error("验证码为6位数字"));return}r()},trigger:"blur"}],newPassword:[{validator:(s,e,r)=>{if(!e){r(new Error("请输入新密码"));return}if(e.length<6){r(new Error("密码长度不能少于6位"));return}if(e.length>20){r(new Error("密码长度不能超过20位"));return}r()},trigger:"blur"}],confirmPassword:[{validator:(s,e,r)=>{if(!e){r(new Error("请确认密码"));return}if(e!==o.newPassword){r(new Error("两次输入的密码不一致"));return}r()},trigger:"blur"}]},N=()=>E(this,null,function*(){try{yield n.value.validateField("email"),w.value=!0;const s=yield P.sendEmailCode(o.email.trim(),"reset-password");s.code===200?(p.success("验证码已发送到您的邮箱"),U()):p.error(s.message||"发送失败，请重试")}catch(s){console.error("发送验证码失败:",s),p.error("发送失败，请重试")}finally{w.value=!1}}),$=()=>E(this,null,function*(){try{yield n.value.validate(),u.value=!0;const s=yield P.verifyEmailCode(o.email.trim(),o.emailCode.trim(),"reset-password");if(s.code!==200){p.error(s.message||"验证码错误");return}const e=yield P.resetPasswordWithCode(o.email.trim(),o.emailCode.trim(),o.newPassword);e.code===200?(p.success("密码重置成功！正在跳转到登录页面..."),setTimeout(()=>{y.push("/login")},1500)):p.error(e.message||"重置失败，请重试")}catch(s){console.error("重置密码失败:",s),p.error("重置失败，请重试")}finally{u.value=!1}}),U=()=>{i.value=60,m=setInterval(()=>{i.value--,i.value<=0&&(clearInterval(m),m=null)},1e3)};return T(()=>{m&&clearInterval(m)}),(s,e)=>{const r=v("el-input"),c=v("el-form-item"),h=v("el-button"),B=v("el-icon"),x=v("el-link"),F=v("el-form");return R(),A("div",j,[f("div",q,[e[9]||(e[9]=f("div",{class:"form-header"},[f("h2",null,"重置密码"),f("p",null,"请输入您的邮箱地址，获取验证码后设置新密码")],-1)),a(F,{ref_key:"forgotFormRef",ref:n,model:o,rules:k,"label-width":"0",size:"large"},{default:l(()=>[a(c,{prop:"email"},{default:l(()=>[a(r,{modelValue:o.email,"onUpdate:modelValue":e[0]||(e[0]=t=>o.email=t),placeholder:"请输入注册邮箱","prefix-icon":"Message",clearable:""},null,8,["modelValue"])]),_:1}),a(c,{prop:"emailCode"},{default:l(()=>[f("div",G,[a(r,{modelValue:o.emailCode,"onUpdate:modelValue":e[1]||(e[1]=t=>o.emailCode=t),placeholder:"请输入邮箱验证码","prefix-icon":"Lock",clearable:"",maxlength:"6"},null,8,["modelValue"]),a(h,{disabled:i.value>0||!o.email,onClick:N,class:"code-btn",loading:w.value},{default:l(()=>[C(b(i.value>0?`${i.value}s`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),a(c,{prop:"newPassword"},{default:l(()=>[a(r,{modelValue:o.newPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>o.newPassword=t),type:"password",placeholder:"请输入新密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),a(c,{prop:"confirmPassword"},{default:l(()=>[a(r,{modelValue:o.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=t=>o.confirmPassword=t),type:"password",placeholder:"请确认新密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),a(c,null,{default:l(()=>[a(h,{type:"primary",loading:u.value,onClick:$,style:{width:"100%"}},{default:l(()=>[u.value?D("",!0):(R(),z(B,{key:0},{default:l(()=>[a(S(W))]),_:1})),C(" "+b(u.value?"重置中...":"重置密码"),1)]),_:1},8,["loading"])]),_:1}),f("div",H,[a(x,{type:"primary",onClick:e[4]||(e[4]=t=>s.$router.push("/login"))},{default:l(()=>e[6]||(e[6]=[C(" 返回登录 ")])),_:1,__:[6]}),e[8]||(e[8]=f("span",{class:"divider"},"|",-1)),a(x,{type:"primary",onClick:e[5]||(e[5]=t=>s.$router.push("/register"))},{default:l(()=>e[7]||(e[7]=[C(" 注册账户 ")])),_:1,__:[7]})])]),_:1},8,["model"])])])}}},Y=I(J,[["__scopeId","data-v-ddd81b22"]]);export{Y as default};
