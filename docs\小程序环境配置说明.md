# 小程序环境配置说明

## 📋 环境概述

小程序支持三种环境配置，每种环境对应不同的API服务器和数据库：

| 环境 | API地址 | 数据库 | 用途 |
|------|---------|--------|------|
| **development** | `http://localhost:3000/api` | 本地数据库 | 本地开发 |
| **test** | `https://www.huanglun.asia/api-test` | 测试数据库 | 线上测试 |
| **production** | `https://www.huanglun.asia/api` | 正式数据库 | 生产环境 |

## 🚀 快速使用

### 开发环境
```bash
npm run dev
```
- 配置开发环境
- 使用本地API服务器
- 启用调试模式

### 测试环境
```bash
npm run test
```
- 配置测试环境
- 使用线上测试API服务器
- 连接测试数据库

### 生产环境
```bash
npm run build
```
- 配置生产环境
- 使用线上正式API服务器
- 连接正式数据库

## 📦 npm包构建

小程序的npm包构建**不需要区分环境**，只需要在微信开发者工具中操作：

### 方法1：使用命令行
```bash
npm run compile:npm
```

### 方法2：使用微信开发者工具
1. 打开微信开发者工具
2. 点击菜单：工具 → 构建npm
3. 等待构建完成

## 🔧 环境配置文件

### .env.development
```env
NODE_ENV=development
API_BASE_URL=http://localhost:3000/api
API_TIMEOUT=10000
WECHAT_APP_ID=wx82283b353918af82
DEBUG=true
```

### .env.test
```env
NODE_ENV=test
API_BASE_URL=https://www.huanglun.asia/api-test
API_TIMEOUT=12000
WECHAT_APP_ID=wx82283b353918af82
DEBUG=true
```

### .env.production
```env
NODE_ENV=production
API_BASE_URL=https://www.huanglun.asia/api
API_TIMEOUT=15000
WECHAT_APP_ID=wx82283b353918af82
DEBUG=false
```

## 🎯 环境自动检测

小程序会根据运行环境自动选择配置：

```javascript
// 自动环境检测逻辑
function getCurrentEnv() {
  try {
    const accountInfo = wx.getAccountInfoSync();
    // 开发版和体验版使用development，正式版使用production
    return accountInfo.miniProgram.envVersion === 'release'
      ? 'production'
      : 'development';
  } catch (e) {
    return 'development';
  }
}
```

### 环境版本对应关系
- **开发版** → `development` 环境
- **体验版** → `development` 环境  
- **正式版** → `production` 环境

## 📱 微信开发者工具配置

### 1. 项目配置
确保 `project.config.json` 中的配置正确：
```json
{
  "appid": "wx82283b353918af82",
  "projectname": "楠楠家厨",
  "setting": {
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  }
}
```

### 2. 构建npm
- 首次使用需要构建npm包
- 代码更新后一般不需要重新构建
- 只有在添加新的npm依赖时才需要重新构建

### 3. 预览和发布
- **预览**：生成二维码，使用测试环境
- **上传**：发布到微信后台，使用生产环境

## 🔍 调试和日志

### 开发环境调试
```javascript
// 开发环境会输出详细日志
console.log('🌍 当前环境: development');
console.log('🔗 API地址: http://localhost:3000/api');
console.log('📱 小程序AppID: wx82283b353918af82');
```

### 生产环境
```javascript
// 生产环境只输出错误日志
// 调试信息被关闭以提升性能
```

## ⚠️ 注意事项

### 1. 环境切换
- 切换环境后需要重新运行对应的npm命令
- 微信开发者工具会自动检测配置变化

### 2. API地址
- 确保API服务器正常运行
- 测试环境和生产环境使用不同的API路径

### 3. 小程序AppID
- 开发和生产使用相同的AppID
- 确保AppID在微信公众平台已配置

### 4. npm包管理
- 小程序npm构建是一次性的
- 不需要为每个环境单独构建
- 只有依赖变化时才需要重新构建

## 🛠️ 故障排查

### 常见问题

1. **API请求失败**
   ```bash
   # 检查环境配置
   npm run dev  # 或 npm run test
   
   # 查看生成的配置文件
   cat config/env.generated.js
   ```

2. **npm包构建失败**
   ```bash
   # 清理并重新构建
   npm run clean:npm
   npm run compile:npm
   ```

3. **环境配置不生效**
   ```bash
   # 重新生成环境配置
   npm run build:env
   ```

### 检查配置
```javascript
// 在小程序中检查当前配置
const config = require('./config/env.js');
console.log('当前环境:', config.currentEnv);
console.log('API地址:', config.baseURL);
```

## ✅ 最佳实践

1. **开发流程**
   - 本地开发使用 `npm run dev`
   - 测试使用 `npm run test`
   - 发布使用 `npm run build`

2. **代码提交**
   - 不要提交 `config/env.generated.js`
   - 环境配置文件 `.env.*` 需要提交

3. **部署流程**
   - 测试环境：`npm run test` → 预览
   - 生产环境：`npm run build` → 上传

**🎉 环境配置已优化，使用更加简单明了！**
