/**
 * 通用工具函数
 */

/**
 * 判断是否为空
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为空
 */
export function isAllEmpty(value) {
  return value === null || value === undefined || value === '';
}

/**
 * 判断是否为布尔值
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为布尔值
 */
export function isBoolean(value) {
  return typeof value === 'boolean';
}

/**
 * 深度比较两个对象是否相等
 * @param {*} a 对象a
 * @param {*} b 对象b
 * @returns {boolean} 是否相等
 */
export function isEqual(a, b) {
  if (a === b) return true;
  
  if (a instanceof Date && b instanceof Date) {
    return a.getTime() === b.getTime();
  }
  
  if (!a || !b || (typeof a !== 'object' && typeof b !== 'object')) {
    return a === b;
  }
  
  if (a === null || a === undefined || b === null || b === undefined) {
    return false;
  }
  
  if (a.prototype !== b.prototype) return false;
  
  let keys = Object.keys(a);
  if (keys.length !== Object.keys(b).length) {
    return false;
  }
  
  return keys.every(k => isEqual(a[k], b[k]));
}

/**
 * 本地存储工具
 */
export function storageLocal() {
  return {
    getItem(key) {
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      } catch (error) {
        console.error('Error getting item from localStorage:', error);
        return null;
      }
    },
    
    setItem(key, value) {
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (error) {
        console.error('Error setting item to localStorage:', error);
      }
    },
    
    removeItem(key) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error('Error removing item from localStorage:', error);
      }
    },
    
    clear() {
      try {
        localStorage.clear();
      } catch (error) {
        console.error('Error clearing localStorage:', error);
      }
    }
  };
}

/**
 * 切换CSS类
 * @param {HTMLElement} element DOM元素
 * @param {string} className 类名
 */
export function toggleClass(element, className) {
  if (!element || !className) return;
  
  if (element.classList.contains(className)) {
    element.classList.remove(className);
  } else {
    element.classList.add(className);
  }
}

/**
 * 检查元素是否包含指定类
 * @param {HTMLElement} element DOM元素
 * @param {string} className 类名
 * @returns {boolean} 是否包含类
 */
export function hasClass(element, className) {
  if (!element || !className) return false;
  return element.classList.contains(className);
}

/**
 * 获取全局属性
 * @returns {object} 全局属性对象
 */
export function useGlobal() {
  const $storage = storageLocal();
  const $config = {
    Version: '1.0.0',
    Title: '楠楠家厨管理系统',
    Layout: 'vertical',
    Theme: 'default',
    DarkMode: false,
    SidebarStatus: true,
    EpThemeColor: '#409EFF',
    ShowLogo: true,
    ShowModel: 'smart',
    HideTabs: false,
    HideFooter: true,
    MultiTagsCache: false,
    KeepAlive: true,
    Locale: 'zh',
    Grey: false,
    Weak: false,
    TooltipEffect: 'light'
  };
  
  return {
    $storage,
    $config
  };
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
