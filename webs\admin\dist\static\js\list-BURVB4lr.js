var oe=Object.defineProperty;var J=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,ae=Object.prototype.propertyIsEnumerable;var L=(s,u,l)=>u in s?oe(s,u,{enumerable:!0,configurable:!0,writable:!0,value:l}):s[u]=l,U=(s,u)=>{for(var l in u||(u={}))ne.call(u,l)&&L(s,l,u[l]);if(J)for(var l of J(u))ae.call(u,l)&&L(s,l,u[l]);return s};var S=(s,u,l)=>new Promise((x,y)=>{var g=_=>{try{C(l.next(_))}catch(f){y(f)}},$=_=>{try{C(l.throw(_))}catch(f){y(f)}},C=_=>_.done?x(_.value):Promise.resolve(_.value).then(g,$);C((l=l.apply(s,u)).next())});import{_ as G,r as A,q as le,f as p,c as E,h as k,d,e as o,t as m,w as t,k as r,m as D,i as B,l as V,a5 as se,z as K,E as h,a as W,o as re,R as de,N as ie,O as ce}from"./index-CtHojCwd.js";import{C as ue}from"./CustomTable-C1GDYDsI.js";import{o as I}from"./order-CyubyprT.js";import{a as T}from"./common-DyWwJEEp.js";const pe={class:"order-detail"},me={class:"detail-header"},ge={class:"order-info"},_e={class:"order-meta"},fe={class:"order-id"},ve={class:"detail-content"},ye={class:"info-section"},be={class:"info-section"},he={class:"info-section"},xe={key:0,class:"info-section"},we={class:"remark-content"},ke={class:"info-section"},Ce={class:"status-actions"},$e={class:"detail-footer"},Te={__name:"OrderDetail",props:{order:{type:Object,required:!0}},emits:["close","statusChanged"],setup(s,{emit:u}){const l=s,x=u,y=A(!1),g=le(()=>{try{return JSON.parse(l.order.items||"[]")}catch(v){return[]}}),$=v=>({pending:"warning",completed:"success",cancelled:"danger"})[v]||"info",C=v=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[v]||"未知",_=v=>S(this,null,function*(){try{const e=C(v);yield K.confirm(`确定要将订单状态改为"${e}"吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),y.value=!0,yield I.updateOrderStatus(l.order.id,v),h.success("状态更新成功"),x("statusChanged",v)}catch(e){e!=="cancel"&&(console.error("更新订单状态失败:",e),h.error("状态更新失败"))}finally{y.value=!1}}),f=()=>{x("close")},P=()=>{var N,O;const v=`
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="text-align: center; margin-bottom: 20px;">订单详情</h2>
      <div style="margin-bottom: 15px;">
        <strong>订单号：</strong>${l.order.id}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用户：</strong>${((N=l.order.user)==null?void 0:N.name)||"未知用户"}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>手机号：</strong>${((O=l.order.user)==null?void 0:O.phone)||"未提供"}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用餐时间：</strong>${T(l.order.mealTime)}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>下单时间：</strong>${T(l.order.createdAt)}
      </div>
      <div style="margin-bottom: 20px;">
        <strong>状态：</strong>${C(l.order.status)}
      </div>
      
      <h3>菜品清单：</h3>
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">菜品名称</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">数量</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">备注</th>
          </tr>
        </thead>
        <tbody>
          ${g.value.map(b=>`
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${b.dishName}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${b.count}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${b.remark||"无"}</td>
            </tr>
          `).join("")}
        </tbody>
      </table>
      
      ${l.order.remark?`
        <div style="margin-bottom: 20px;">
          <strong>订单备注：</strong>${l.order.remark}
        </div>
      `:""}
      
      <div style="text-align: center; margin-top: 30px; color: #666;">
        打印时间：${T(new Date)}
      </div>
    </div>
  `,e=window.open("","_blank");e.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>订单详情 - ${l.order.id}</title>
      <style>
        @media print {
          body { margin: 0; }
          @page { margin: 1cm; }
        }
      </style>
    </head>
    <body>
      ${v}
    </body>
    </html>
  `),e.document.close(),e.print(),e.close(),h.success("打印任务已发送")};return(v,e)=>{const N=p("el-tag"),O=p("el-divider"),b=p("el-descriptions-item"),Y=p("el-descriptions"),z=p("el-col"),R=p("el-row"),M=p("el-table-column"),j=p("el-table"),a=p("el-button"),n=p("el-icon");return k(),E("div",pe,[d("div",me,[d("div",ge,[e[4]||(e[4]=d("h3",null,"订单详情",-1)),d("div",_e,[d("span",fe,"订单号："+m(s.order.id),1),o(N,{type:$(s.order.status),size:"large"},{default:t(()=>[r(m(C(s.order.status)),1)]),_:1},8,["type"])])])]),o(O),d("div",ve,[o(R,{gutter:20},{default:t(()=>[o(z,{span:12},{default:t(()=>[d("div",ye,[e[5]||(e[5]=d("h4",null,"用户信息",-1)),o(Y,{column:1,border:"",size:"small"},{default:t(()=>[o(b,{label:"姓名"},{default:t(()=>{var i;return[r(m(((i=s.order.user)==null?void 0:i.name)||"未知用户"),1)]}),_:1}),o(b,{label:"手机号"},{default:t(()=>{var i;return[r(m(((i=s.order.user)==null?void 0:i.phone)||"未提供"),1)]}),_:1}),o(b,{label:"微信昵称"},{default:t(()=>{var i;return[r(m(((i=s.order.user)==null?void 0:i.nickname)||"未提供"),1)]}),_:1})]),_:1})])]),_:1}),o(z,{span:12},{default:t(()=>[d("div",be,[e[6]||(e[6]=d("h4",null,"订单信息",-1)),o(Y,{column:1,border:"",size:"small"},{default:t(()=>[o(b,{label:"用餐时间"},{default:t(()=>[r(m(B(T)(s.order.mealTime)),1)]),_:1}),o(b,{label:"下单时间"},{default:t(()=>[r(m(B(T)(s.order.createdAt)),1)]),_:1}),o(b,{label:"更新时间"},{default:t(()=>[r(m(B(T)(s.order.updatedAt)),1)]),_:1})]),_:1})])]),_:1})]),_:1}),d("div",he,[e[7]||(e[7]=d("h4",null,"菜品清单",-1)),o(j,{data:g.value,border:"",size:"small"},{default:t(()=>[o(M,{prop:"dishName",label:"菜品名称"}),o(M,{prop:"count",label:"数量",width:"80",align:"center"}),o(M,{label:"备注",prop:"remark","show-overflow-tooltip":""},{default:t(({row:i})=>[r(m(i.remark||"无"),1)]),_:1})]),_:1},8,["data"])]),s.order.remark?(k(),E("div",xe,[e[8]||(e[8]=d("h4",null,"订单备注",-1)),d("div",we,m(s.order.remark),1)])):D("",!0),d("div",ke,[e[13]||(e[13]=d("h4",null,"状态操作",-1)),d("div",Ce,[s.order.status==="pending"?(k(),V(a,{key:0,type:"success",onClick:e[0]||(e[0]=i=>_("completed")),loading:y.value},{default:t(()=>e[9]||(e[9]=[r(" 标记为已完成 ")])),_:1,__:[9]},8,["loading"])):D("",!0),s.order.status==="pending"?(k(),V(a,{key:1,type:"danger",onClick:e[1]||(e[1]=i=>_("cancelled")),loading:y.value},{default:t(()=>e[10]||(e[10]=[r(" 取消订单 ")])),_:1,__:[10]},8,["loading"])):D("",!0),s.order.status==="cancelled"?(k(),V(a,{key:2,type:"primary",onClick:e[2]||(e[2]=i=>_("pending")),loading:y.value},{default:t(()=>e[11]||(e[11]=[r(" 恢复订单 ")])),_:1,__:[11]},8,["loading"])):D("",!0),s.order.status==="completed"?(k(),V(a,{key:3,type:"warning",onClick:e[3]||(e[3]=i=>_("pending")),loading:y.value},{default:t(()=>e[12]||(e[12]=[r(" 重新处理 ")])),_:1,__:[12]},8,["loading"])):D("",!0)])])]),d("div",$e,[o(a,{onClick:f},{default:t(()=>e[14]||(e[14]=[r("关闭")])),_:1,__:[14]}),o(a,{type:"primary",onClick:P},{default:t(()=>[o(n,null,{default:t(()=>[o(B(se))]),_:1}),e[15]||(e[15]=r(" 打印订单 "))]),_:1,__:[15]})])])}}},Ne=G(Te,[["__scopeId","data-v-5c4b0ebb"]]),Oe={class:"order-list"},De={class:"user-info"},ze={__name:"list",setup(s){const u=A(!1),l=A([]),x=A(!1),y=A({}),g=W({page:1,size:10,total:0}),$=W({status:"",userName:"",startDate:"",endDate:""}),C=[{prop:"id",label:"订单号",width:180},{prop:"userName",label:"用户信息",slot:"userName",width:140},{prop:"items",label:"菜品",slot:"items",width:80},{prop:"status",label:"状态",slot:"status",width:80},{prop:"mealTime",label:"用餐时间",slot:"mealTime",width:120},{prop:"remark",label:"备注",showOverflowTooltip:!0,width:120},{prop:"createdAt",label:"下单时间",slot:"createdAt",width:140},{prop:"actions",label:"操作",slot:"actions",width:240,fixed:"right"}],_=[{prop:"status",label:"状态",type:"select",placeholder:"选择状态",options:[{label:"待处理",value:"pending"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}]},{prop:"userName",label:"用户名",type:"input",placeholder:"请输入用户名"},{prop:"startDate",label:"开始日期",type:"date",placeholder:"选择开始日期"},{prop:"endDate",label:"结束日期",type:"date",placeholder:"选择结束日期"}],f=()=>S(this,null,function*(){u.value=!0;try{const a=U({page:g.page,size:g.size},$),n=yield I.getOrders(a);n.code===200?(l.value=n.data.list||[],g.total=n.data.total||0):h.error(n.message||"加载数据失败")}catch(a){console.error("加载订单列表失败:",a),h.error("加载数据失败")}finally{u.value=!1}}),P=a=>{Object.assign($,a),g.page=1,f()},v=()=>{Object.keys($).forEach(a=>{$[a]=""}),g.page=1,f()},e=a=>{g.page=a,f()},N=a=>{g.size=a,g.page=1,f()},O=a=>{y.value=a,x.value=!0},b=(a,n)=>S(this,null,function*(){try{const i=yield I.updateOrderStatus(a.id,n);i.code===200?(h.success("状态更新成功"),f()):h.error(i.message||"状态更新失败")}catch(i){console.error("更新订单状态失败:",i),h.error("状态更新失败")}}),Y=a=>S(this,null,function*(){try{yield K.confirm("确定要删除这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const n=yield I.deleteOrder(a.id);n.code===200?(h.success("删除成功"),f()):h.error(n.message||"删除失败")}catch(n){n!=="cancel"&&(console.error("删除订单失败:",n),h.error("删除失败"))}}),z=a=>{try{return JSON.parse(a||"[]")}catch(n){return[]}},R=a=>({pending:"warning",completed:"success",cancelled:"danger"})[a]||"info",M=a=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[a]||"未知",j=a=>T(a,"YYYY-MM-DD HH:mm");return re(()=>{f()}),(a,n)=>{const i=p("el-tooltip"),Q=p("el-tag"),q=p("el-button"),X=p("el-icon"),F=p("el-dropdown-item"),Z=p("el-dropdown-menu"),ee=p("el-dropdown"),te=p("el-dialog");return k(),E("div",Oe,[o(ue,{title:"订单管理",data:l.value,columns:C,loading:u.value,pagination:g,"show-search":!0,"search-fields":_,onSearch:P,onReset:v,onCurrentChange:e,onSizeChange:N},{userName:t(({row:c})=>{var w,H;return[d("div",De,[d("span",null,m(((w=c.user)==null?void 0:w.name)||"未知用户"),1),d("small",null,m((H=c.user)==null?void 0:H.phone),1)])]}),items:t(({row:c})=>[o(i,{effect:"dark",placement:"top"},{content:t(()=>[(k(!0),E(ie,null,ce(z(c.items),w=>(k(),E("div",{key:w.dishName},m(w.dishName)+" x"+m(w.count),1))),128))]),default:t(()=>[d("span",null,m(z(c.items).length)+"道菜",1)]),_:2},1024)]),status:t(({row:c})=>[o(Q,{type:R(c.status)},{default:t(()=>[r(m(M(c.status)),1)]),_:2},1032,["type"])]),mealTime:t(({row:c})=>[r(m(j(c.mealTime)),1)]),createdAt:t(({row:c})=>[r(m(j(c.createdAt)),1)]),actions:t(({row:c})=>[o(q,{size:"small",onClick:w=>O(c)},{default:t(()=>n[2]||(n[2]=[r("查看")])),_:2,__:[2]},1032,["onClick"]),o(ee,{onCommand:w=>b(c,w)},{dropdown:t(()=>[o(Z,null,{default:t(()=>[o(F,{command:"pending"},{default:t(()=>n[4]||(n[4]=[r("待处理")])),_:1,__:[4]}),o(F,{command:"completed"},{default:t(()=>n[5]||(n[5]=[r("已完成")])),_:1,__:[5]}),o(F,{command:"cancelled"},{default:t(()=>n[6]||(n[6]=[r("已取消")])),_:1,__:[6]})]),_:1})]),default:t(()=>[o(q,{size:"small",type:"primary"},{default:t(()=>[n[3]||(n[3]=r(" 更新状态")),o(X,{class:"el-icon--right"},{default:t(()=>[o(B(de))]),_:1})]),_:1,__:[3]})]),_:2},1032,["onCommand"]),o(q,{size:"small",type:"danger",onClick:w=>Y(c)},{default:t(()=>n[7]||(n[7]=[r("删除")])),_:2,__:[7]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),o(te,{modelValue:x.value,"onUpdate:modelValue":n[1]||(n[1]=c=>x.value=c),title:"订单详情",width:"600px"},{default:t(()=>[x.value?(k(),V(Ne,{key:0,order:y.value,onClose:n[0]||(n[0]=c=>x.value=!1)},null,8,["order"])):D("",!0)]),_:1},8,["modelValue"])])}}},Ee=G(ze,[["__scopeId","data-v-1067fadc"]]);export{Ee as default};
