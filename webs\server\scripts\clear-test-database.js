#!/usr/bin/env node
/**
 * 清空测试数据库脚本
 * 用于清理测试环境的数据
 */

const { PrismaClient } = require('@prisma/client');

// 使用测试数据库连接
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "mysql://nannan_user:5201314hl@8.148.231.104:3306/nannan_db_test"
    }
  }
});

async function clearTestDatabase() {
  try {
    console.log('🗑️  开始清空测试数据库...');
    
    // 按照外键依赖关系的顺序删除数据
    console.log('📋 删除菜单项...');
    await prisma.menuItem.deleteMany({});
    
    console.log('📋 删除菜单...');
    await prisma.menu.deleteMany({});
    
    console.log('📦 删除订单项...');
    await prisma.orderItem.deleteMany({});
    
    console.log('📦 删除订单...');
    await prisma.order.deleteMany({});
    
    console.log('🔔 删除通知...');
    await prisma.notification.deleteMany({});
    
    console.log('💬 删除消息...');
    await prisma.message.deleteMany({});
    
    console.log('🔗 删除用户连接...');
    await prisma.userConnection.deleteMany({});
    
    console.log('🍽️  删除菜品...');
    await prisma.dish.deleteMany({});
    
    console.log('📂 删除菜品分类...');
    await prisma.dishCategory.deleteMany({});
    
    console.log('👥 删除用户（保留管理员）...');
    // 保留管理员用户，只删除普通用户
    await prisma.user.deleteMany({
      where: {
        role: {
          not: 'admin'
        }
      }
    });
    
    console.log('✅ 测试数据库清空完成！');
    
    // 显示剩余数据统计
    const remainingUsers = await prisma.user.count();
    const remainingDishes = await prisma.dish.count();
    const remainingOrders = await prisma.order.count();
    const remainingMessages = await prisma.message.count();
    
    console.log('\n📊 剩余数据统计:');
    console.log(`👥 用户: ${remainingUsers}`);
    console.log(`🍽️  菜品: ${remainingDishes}`);
    console.log(`📦 订单: ${remainingOrders}`);
    console.log(`💬 消息: ${remainingMessages}`);
    
  } catch (error) {
    console.error('❌ 清空数据库失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 确认操作
function confirmClear() {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('⚠️  确定要清空测试数据库吗？这将删除所有测试数据！(y/N): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 主函数
async function main() {
  try {
    console.log('🔍 连接到测试数据库: nannan_db_test');
    
    // 确认操作
    const confirmed = await confirmClear();
    if (!confirmed) {
      console.log('❌ 操作已取消');
      return;
    }
    
    await clearTestDatabase();
    console.log('\n🎉 测试数据库清空成功！');
    
  } catch (error) {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { clearTestDatabase };
