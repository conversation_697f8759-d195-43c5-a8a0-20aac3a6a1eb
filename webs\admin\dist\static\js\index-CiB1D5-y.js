var y=(h,g,c)=>new Promise((u,d)=>{var f=m=>{try{b(c.next(m))}catch(v){d(v)}},o=m=>{try{b(c.throw(m))}catch(v){d(v)}},b=m=>m.done?u(m.value):Promise.resolve(m.value).then(f,o);b((c=c.apply(h,g)).next())});import{_ as A,u as O,r as R,a as D,o as L,E as p,b as P,n as U,c as F,d as t,e as r,w as i,f as _,g as z,h as V,i as T,j as B,k as S,l as $,m as K,p as j,t as q}from"./index-CtHojCwd.js";const W={class:"login-container"},X={class:"login-box"},Y={class:"login-header"},Z={class:"logo"},G={class:"login-options flex justify-between"},H={class:"register-link"},J={__name:"index",setup(h){const g=P(),c=O(),u=R(),d=R(!1),f=R(!1),o=D({username:"",password:""}),v={username:[{validator:(n,e,s)=>{if(!e){s(new Error("请输入用户名/手机号/邮箱"));return}const l=/^1[3-9]\d{9}$/,a=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,E=/^[a-zA-Z0-9_]{3,20}$/;l.test(e)||a.test(e)||E.test(e)?s():s(new Error("请输入正确的用户名、手机号或邮箱"))},trigger:"blur"}],password:[{validator:(n,e,s)=>{if(!e){s(new Error("请输入密码"));return}if(e.length<6){s(new Error("密码长度不能少于6位"));return}if(e.length>20){s(new Error("密码长度不能超过20位"));return}s()},trigger:"blur"}]},x=()=>y(this,null,function*(){if(u.value)try{if(!(yield u.value.validate()))return;d.value=!0,p.info("正在验证登录信息...");const e=yield c.login({username:o.username.trim(),password:o.password,loginType:"password"});if(e.success){p.success("登录成功！正在跳转..."),f.value?(localStorage.setItem("remembered_username",o.username.trim()),localStorage.setItem("remember_me","true")):(localStorage.removeItem("remembered_username"),localStorage.removeItem("remember_me")),yield U();const s=sessionStorage.getItem("login_redirect"),l=s||"/";s&&sessionStorage.removeItem("login_redirect"),setTimeout(()=>{g.push(l)},500)}else{const s=k(e.message,e.code);p.error(s)}}catch(n){console.error("登录错误:",n);let e="登录失败，请重试";if(n.response){const s=n.response.status;s===401?e="用户名或密码错误":s===403?e="账户已被禁用，请联系管理员":s===429?e="登录尝试次数过多，请稍后再试":s>=500&&(e="服务器错误，请稍后再试")}else n.code==="NETWORK_ERROR"&&(e="网络连接失败，请检查网络设置");p.error(e)}finally{d.value=!1}}),k=(n,e)=>({INVALID_CREDENTIALS:"用户名或密码错误",ACCOUNT_DISABLED:"账户已被禁用，请联系管理员",ACCOUNT_LOCKED:"账户已被锁定，请稍后再试",TOO_MANY_ATTEMPTS:"登录尝试次数过多，请稍后再试",USER_NOT_FOUND:"用户不存在",PASSWORD_EXPIRED:"密码已过期，请重置密码"})[e]||n||"登录失败，请重试",C=()=>{g.push("/forgot-password")},N=()=>{g.push("/register")};return L(()=>{if(c.isLoggedIn){p.info("您已登录，正在跳转..."),g.push("/");return}const n=new URLSearchParams(window.location.search),e=n.get("username");if(e)o.username=e,p.success("注册成功！请输入密码登录");else{const l=localStorage.getItem("remembered_username"),a=localStorage.getItem("remember_me");l&&a==="true"&&(o.username=l,f.value=!0)}U(()=>{if(u.value){const l=u.value.$el.querySelector('input[placeholder*="用户名"]');if(l&&!o.username)l.focus();else if(o.username){const a=u.value.$el.querySelector('input[type="password"]');a&&a.focus()}}});const s=n.get("redirect");s&&sessionStorage.setItem("login_redirect",s)}),(n,e)=>{const s=_("el-icon"),l=_("el-input"),a=_("el-form-item"),E=_("el-checkbox"),I=_("el-button"),M=_("el-form");return V(),F("div",W,[e[10]||(e[10]=t("div",{class:"login-background"},[t("div",{class:"bg-shape shape-1"}),t("div",{class:"bg-shape shape-2"}),t("div",{class:"bg-shape shape-3"})],-1)),t("div",X,[t("div",Y,[t("div",Z,[r(s,{size:"48",color:"#409eff"},{default:i(()=>[r(T(B))]),_:1})]),e[3]||(e[3]=t("h1",null,"楠楠家厨管理系统",-1)),e[4]||(e[4]=t("p",null,"欢迎登录后台管理系统",-1))]),r(M,{ref_key:"loginFormRef",ref:u,model:o,rules:v,class:"login-form",onKeyup:z(x,["enter"])},{default:i(()=>[r(a,{prop:"username"},{default:i(()=>[r(l,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=w=>o.username=w),placeholder:"请输入用户名/手机号",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),r(a,{prop:"password"},{default:i(()=>[r(l,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=w=>o.password=w),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),r(a,null,{default:i(()=>[t("div",G,[r(E,{modelValue:f.value,"onUpdate:modelValue":e[2]||(e[2]=w=>f.value=w)},{default:i(()=>e[5]||(e[5]=[S("记住我")])),_:1,__:[5]},8,["modelValue"]),r(I,{link:"",class:"forgot-password-btn",onClick:C},{default:i(()=>e[6]||(e[6]=[S(" 忘记密码？ ")])),_:1,__:[6]})])]),_:1}),r(a,null,{default:i(()=>[r(I,{type:"primary",size:"large",loading:d.value,onClick:x,class:"login-btn"},{default:i(()=>[d.value?K("",!0):(V(),$(s,{key:0},{default:i(()=>[r(T(j))]),_:1})),S(" "+q(d.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1}),r(a,null,{default:i(()=>[t("div",H,[e[8]||(e[8]=t("span",null,"还没有账户？",-1)),r(I,{link:"",class:"register-btn",onClick:N},{default:i(()=>e[7]||(e[7]=[S(" 立即注册 ")])),_:1,__:[7]})])]),_:1})]),_:1},8,["model"]),e[9]||(e[9]=t("div",{class:"login-footer"},[t("p",null,"© 2024 楠楠家厨管理系统. All rights reserved.")],-1))])])}}},se=A(J,[["__scopeId","data-v-8a414c6f"]]);export{se as default};
