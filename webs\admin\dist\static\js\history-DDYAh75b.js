var se=Object.defineProperty,oe=Object.defineProperties;var ne=Object.getOwnPropertyDescriptors;var E=Object.getOwnPropertySymbols;var le=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var N=(i,s,a)=>s in i?se(i,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[s]=a,W=(i,s)=>{for(var a in s||(s={}))le.call(s,a)&&N(i,a,s[a]);if(E)for(var a of E(s))re.call(s,a)&&N(i,a,s[a]);return i},O=(i,s)=>oe(i,ne(s));var M=(i,s,a)=>new Promise((v,c)=>{var r=m=>{try{T(a.next(m))}catch(z){c(z)}},x=m=>{try{T(a.throw(m))}catch(z){c(z)}},T=m=>m.done?v(m.value):Promise.resolve(m.value).then(r,x);T((a=a.apply(i,s)).next())});import{_ as ie,r as B,a as P,q as ce,o as de,c as k,e as l,w as o,f as V,E as g,h as b,k as f,t as u,i as ue,a1 as pe,m as $,d as p,N as j,O as F,z as H,K as Y}from"./index-CtHojCwd.js";import{C as me}from"./CustomTable-C1GDYDsI.js";import{m as A}from"./menu-CzZqR-71.js";const fe={class:"menu-history"},he={key:0,class:"menu-detail"},_e={class:"detail-header"},ye={class:"menu-stats"},ge={class:"dish-categories"},ve={class:"category-title"},Ce={class:"dish-grid"},ke={class:"dish-image"},be={class:"dish-info"},xe={class:"dish-name"},we={class:"dish-price"},De={key:0,class:"dish-desc"},Me={__name:"history",setup(i){const s=B(!1),a=B([]),v=B(!1),c=B(null),r=P({page:1,size:10,total:0}),x=P({date:"",status:""}),T=B([{key:"hot",name:"热菜"},{key:"cold",name:"凉菜"},{key:"soup",name:"汤品"},{key:"staple",name:"主食"},{key:"dessert",name:"甜品"}]),m=[{prop:"date",label:"日期",minWidth:120,formatter:e=>R(e.date)},{prop:"dishCount",label:"菜品数量",minWidth:100,slot:!0},{prop:"totalPrice",label:"总价值",minWidth:100,formatter:e=>`¥${e.totalPrice||0}`},{prop:"status",label:"状态",minWidth:100,slot:!0},{prop:"createdBy",label:"创建人",minWidth:150,showOverflowTooltip:!0},{prop:"createdAt",label:"创建时间",minWidth:160,formatter:e=>S(e.createdAt)},{prop:"updatedAt",label:"更新时间",minWidth:160,formatter:e=>S(e.updatedAt)},{label:"操作",width:150,slot:"operation",fixed:"right"}],z=[{prop:"date",label:"日期",type:"date"},{prop:"status",label:"状态",type:"select",options:[{label:"今日菜单",value:"today"},{label:"已过期",value:"expired"},{label:"正常",value:"active"}]}],I=ce(()=>!c.value||!c.value.dishes?[]:T.value.map(e=>O(W({},e),{dishes:c.value.dishes.filter(t=>t.category===e.key)})).filter(e=>e.dishes.length>0)),w=()=>M(this,null,function*(){s.value=!0;try{const e=W({page:r.page,size:r.size},x),t=yield A.getHistoryMenus(e);t.code===200&&t.data?(a.value=t.data.list||[],r.total=t.data.total||0):(g.error(t.message||"加载数据失败"),a.value=[],r.total=0)}catch(e){console.error("加载历史菜单失败:",e),g.error("加载数据失败"),a.value=[],r.total=0}finally{s.value=!1}}),R=e=>Y(e).format("MM-DD"),S=e=>Y(e).format("YYYY-MM-DD HH:mm"),q=e=>({today:"success",active:"primary",expired:"warning",deleted:"danger"})[e]||"info",K=e=>({today:"今日菜单",active:"正常",expired:"已过期",deleted:"已删除"})[e]||e,L=e=>!e||!Array.isArray(e)?0:e.reduce((t,d)=>t+(d.price||0),0),U=e=>{Object.assign(x,e),r.page=1,w()},G=()=>{Object.keys(x).forEach(e=>{x[e]=""}),r.page=1,w()},J=e=>{r.page=e,w()},Q=e=>{r.size=e,r.page=1,w()},X=e=>{c.value=e,v.value=!0},Z=e=>M(this,null,function*(){try{yield H.confirm("确定要将此菜单复制为今日菜单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const t={date:Y().format("YYYY-MM-DD"),dishes:e.dishes.map(d=>({dishId:d.id,dishName:d.name,category:d.category,price:d.price,image:d.image}))};yield A.createMenu(t),g.success("菜单复制成功")}catch(t){t!=="cancel"&&(console.error("复制菜单失败:",t),g.error("复制菜单失败"))}}),ee=()=>M(this,null,function*(){try{yield Z(c.value),v.value=!1}catch(e){}}),te=e=>M(this,null,function*(){var t,d,h,C;try{yield H.confirm("确定要删除这个菜单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield A.deleteMenu(e.id),g.success("删除成功"),w()}catch(_){if(_!=="cancel"){console.error("删除菜单失败:",_);let D="删除失败";((t=_.response)==null?void 0:t.status)===403?D="没有权限删除此菜单":((d=_.response)==null?void 0:d.status)===404?D="菜单不存在或已被删除":(C=(h=_.response)==null?void 0:h.data)!=null&&C.message&&(D=_.response.data.message),g.error(D)}}}),ae=()=>M(this,null,function*(){try{g.info("导出功能开发中...")}catch(e){console.error("导出失败:",e),g.error("导出失败")}});return de(()=>{w()}),(e,t)=>{const d=V("el-icon"),h=V("el-button"),C=V("el-tag"),_=V("el-image"),D=V("el-dialog");return b(),k("div",fe,[l(me,{title:"历史菜单",data:a.value,columns:m,loading:s.value,pagination:r,"show-search":!0,"search-fields":z,onSearch:U,onReset:G,onCurrentChange:J,onSizeChange:Q},{actions:o(()=>[l(h,{type:"primary",onClick:ae},{default:o(()=>[l(d,null,{default:o(()=>[l(ue(pe))]),_:1}),t[2]||(t[2]=f(" 导出数据 "))]),_:1,__:[2]})]),dishCount:o(({row:n})=>[l(C,{type:"info"},{default:o(()=>[f(u(n.dishCount)+"道菜",1)]),_:2},1024)]),status:o(({row:n})=>[l(C,{type:q(n.status)},{default:o(()=>[f(u(K(n.status)),1)]),_:2},1032,["type"])]),operation:o(({row:n})=>[l(h,{size:"small",onClick:y=>X(n)},{default:o(()=>t[3]||(t[3]=[f("查看")])),_:2,__:[3]},1032,["onClick"]),l(h,{size:"small",type:"danger",onClick:y=>te(n)},{default:o(()=>t[4]||(t[4]=[f("删除")])),_:2,__:[4]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),l(D,{modelValue:v.value,"onUpdate:modelValue":t[1]||(t[1]=n=>v.value=n),title:"菜单详情",width:"900px"},{footer:o(()=>[l(h,{onClick:t[0]||(t[0]=n=>v.value=!1)},{default:o(()=>t[5]||(t[5]=[f("关闭")])),_:1,__:[5]}),l(h,{type:"primary",onClick:ee},{default:o(()=>t[6]||(t[6]=[f(" 复制为今日菜单 ")])),_:1,__:[6]})]),default:o(()=>[c.value?(b(),k("div",he,[p("div",_e,[p("h3",null,u(c.value.date)+" 菜单",1),p("div",ye,[l(C,null,{default:o(()=>[f("共 "+u(c.value.dishCount)+" 道菜",1)]),_:1}),l(C,{type:"success",class:"ml-2"},{default:o(()=>[f(" 总价值 ¥"+u(L(c.value.dishes)),1)]),_:1})])]),p("div",ge,[(b(!0),k(j,null,F(I.value,n=>(b(),k("div",{key:n.key,class:"category-section"},[p("h4",ve,u(n.name)+" ("+u(n.dishes.length)+") ",1),p("div",Ce,[(b(!0),k(j,null,F(n.dishes,y=>(b(),k("div",{key:y.id,class:"dish-card"},[p("div",ke,[l(_,{src:y.image,fit:"cover"},null,8,["src"])]),p("div",be,[p("h5",xe,u(y.name),1),p("p",we,"¥"+u(y.price),1),y.description?(b(),k("p",De,u(y.description),1)):$("",!0)])]))),128))])]))),128))])])):$("",!0)]),_:1},8,["modelValue"])])}}},We=ie(Me,[["__scopeId","data-v-71fa34af"]]);export{We as default};
