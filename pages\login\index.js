const app = getApp();
const {userApi} = require('../../services/api');

Page({
  data: {
    username: '',
    password: '',
    loginTip: '',
    rememberPwd: false,
    loading: false,
    loginType: 'wechat', // 默认使用密码登录
    agreedToTerms: false, // 用户协议同意状态
    registerForm: {
      name: '',
      phone: '',
      password: '',
      confirmPassword: ''
    }
  },

  onLoad() {
    // 页面加载时的逻辑
    // 检查是否有保存的账号密码
    const savedAccount = wx.getStorageSync('savedAccount');
    if (savedAccount) {
      this.setData({
        username: savedAccount.username,
        password: savedAccount.password,
        rememberPwd: true
      });
    }

    // 检查是否支持微信登录
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 用户已经授权过，可以直接调用 getUserInfo 获取头像昵称
          this.setData({
            loginType: 'wechat' // 如果已授权，默认使用微信登录
          });
        }
      }
    });
  },

  // 切换用户协议同意状态
  toggleAgreement() {
    this.setData({
      agreedToTerms: !this.data.agreedToTerms
    });
  },

  // 显示隐私政策页面
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    });
  },

  // 切换登录方式
  switchLoginType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      loginType: type,
      loginTip: '' // 清空提示信息
    });
  },

  // 输入账号
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value.trim()
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value.trim()
    });
  },

  // 注册表单输入事件
  onNameInput(e) {
    this.setData({
      'registerForm.name': e.detail.value.trim()
    });
  },

  onRegisterPhoneInput(e) {
    this.setData({
      'registerForm.phone': e.detail.value.trim()
    });
  },

  onRegisterPasswordInput(e) {
    this.setData({
      'registerForm.password': e.detail.value.trim()
    });
  },

  onConfirmPasswordInput(e) {
    this.setData({
      'registerForm.confirmPassword': e.detail.value.trim()
    });
  },

  // 切换到登录
  switchToLogin() {
    this.setData({
      loginType: 'password',
      loginTip: ''
    });
  },

  // 切换记住密码
  toggleRememberPwd() {
    this.setData({
      rememberPwd: !this.data.rememberPwd
    });
  },

  // 忘记密码
  forgetPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请到后台通过邮箱重置密码!',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 账号密码登录
  async loginWithPassword() {
    const {username, password, rememberPwd} = this.data;

    // 验证输入
    if (!username) {
      this.setData({
        loginTip: '请输入手机号或账号'
      });
      return;
    }

    if (!password) {
      this.setData({
        loginTip: '请输入密码'
      });
      return;
    }

    // 验证密码长度
    if (password.length < 6) {
      this.setData({
        loginTip: '密码至少需要6位字符'
      });
      return;
    }

    // 验证手机号格式
    // if (!/^1\d{10}$/.test(username)) {
    //   this.setData({
    //     loginTip: '请输入正确的手机号'
    //   });
    //   return;
    // }

    // 设置加载状态
    this.setData({
      loading: true,
      loginTip: '登录中...'
    });

    try {
      // 调用登录 API
      const res = await userApi.login({
        username,
        password,
        loginType: 'password'
      });
      // 登录成功
      this.setData({
        loginTip: '登录成功',
        loading: false
      });

      // 如果选择了记住密码，保存账号密码
      if (rememberPwd) {
        wx.setStorageSync('savedAccount', {
          username,
          password
        });
      } else {
        wx.removeStorageSync('savedAccount');
      }

      // 保存用户信息和 token
      this.saveUserInfoAndRedirect(res.data);
    } catch (error) {
      console.error('登录失败:', error);

      this.setData({
        loginTip: error.message || '登录失败，请重试',
        loading: false
      });
    }
  },

  // 微信一键登录（免费版本，只使用openid）
  async loginWithWechat() {
    // 检查是否同意用户协议
    if (!this.data.agreedToTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }

    try {
      // 获取微信登录凭证
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: res => resolve(res),
          fail: err => reject(err)
        });
      });

      if (!loginRes.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 调用登录API（只使用code，不需要手机号）
      this.setData({
        loginTip: '登录中...'
      });

      const res = await userApi.login({
        code: loginRes.code,
        loginType: 'wechat'
      });

      // 登录成功
      this.setData({
        loginTip: '登录成功',
        loading: false
      });

      // 保存用户信息和 token
      this.saveUserInfoAndRedirect(res.data);

      // 微信登录成功后请求订阅授权
      this.requestSubscribeAfterLogin();
    } catch (error) {
      console.error('微信登录失败:', error);

      this.setData({
        loginTip: error.message || '微信登录失败，请重试',
        loading: false
      });
    }
  },

  // 微信登录后请求订阅授权
  async requestSubscribeAfterLogin() {
    try {
      console.log('🔔 微信登录成功，请求订阅授权');

      // 延迟1秒后请求授权，避免与登录流程冲突
      setTimeout(async () => {
        try {
          const result = await new Promise((resolve, reject) => {
            wx.requestSubscribeMessage({
              tmplIds: ['kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc'],
              success: res => {
                resolve(res);
              },
              fail: err => {
                resolve(null); // 不阻断登录流程
              }
            });
          });

          if (result) {
            const templateId = 'kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc';
            const authResult = result[templateId];

            if (authResult === 'accept') {
              wx.showToast({
                title: '订阅成功，可接收通知',
                icon: 'success',
                duration: 2000
              });
            }
          }
        } catch (error) {
          // 静默处理订阅授权异常
        }
      }, 1000);
    } catch (error) {
      // 静默处理请求订阅授权失败
    }
  },

  // 保存用户信息并跳转
  saveUserInfoAndRedirect(data) {
    // 保存令牌信息（支持新旧格式）
    if (data && data.accessToken) {
      // 新的单点登录格式
      wx.setStorageSync('accessToken', data.accessToken);
      wx.setStorageSync('refreshToken', data.refreshToken);
      wx.setStorageSync('tokenType', data.tokenType);
      wx.setStorageSync('expiresIn', data.expiresIn);

      // 为了兼容性，也保存到旧的 token 字段
      wx.setStorageSync('token', data.accessToken);
    } else if (data && data.token) {
      // 旧格式兼容
      wx.setStorageSync('token', data.token);
    }

    if (data && data.user) {
      // 保存用户 ID
      wx.setStorageSync('userId', data.user.id);
      wx.setStorageSync('userInfo', data.user);

      // 将用户信息存入全局数据 - 刷新会丢失
      if (app.globalData) {
        app.globalData.userInfo = data.user;
      }

      // 检查用户是否有手机号码
      this.checkUserPhoneAndRedirect(data.user);
    } else {
      // 如果没有用户信息，直接跳转到首页
      this.redirectToHome();
    }
  },

  // 检查用户手机号并决定跳转路径
  checkUserPhoneAndRedirect(user) {
    // 检查用户是否有手机号码
    if (!user.phone || user.phone.trim() === '') {
      console.log('用户没有手机号码，标记为需要完善信息');

      // 标记用户需要完善信息（首页会检查这个标记）
      wx.setStorageSync('needCompleteProfile', true);
      wx.setStorageSync('isFirstLogin', true);

      // 直接跳转到首页，不强制进入个人资料页面
      this.redirectToHome();
    } else {
      // 有手机号码，正常跳转到首页
      this.redirectToHome();
    }
  },

  // 跳转到首页
  redirectToHome() {
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }, 800);
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    });
  },

  // 账号密码注册
  async registerWithPassword() {
    const {registerForm} = this.data;
    const {name, phone, password, confirmPassword} = registerForm;

    // 验证输入
    if (!name || !phone || !password || !confirmPassword) {
      this.setData({
        loginTip: '请填写完整信息'
      });
      return;
    }

    // 验证姓名
    if (name.length < 2) {
      this.setData({
        loginTip: '姓名至少2个字符'
      });
      return;
    }

    // 验证手机号格式
    if (!/^1\d{10}$/.test(phone)) {
      this.setData({
        loginTip: '请输入正确的手机号'
      });
      return;
    }

    // 验证密码长度
    if (password.length < 6) {
      this.setData({
        loginTip: '密码至少6位'
      });
      return;
    }

    // 验证密码确认
    if (password !== confirmPassword) {
      this.setData({
        loginTip: '两次密码输入不一致'
      });
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true,
      loginTip: '注册中...'
    });

    try {
      // 调用注册 API
      const res = await userApi.register({
        name,
        phone,
        password
      });

      // 注册成功
      this.setData({
        loginTip: '注册成功，正在登录...',
        loading: false
      });

      // 保存用户信息和 token
      this.saveUserInfoAndRedirect(res.data);
    } catch (error) {
      console.error('注册失败:', error);

      this.setData({
        loginTip: error.message || '注册失败，请重试',
        loading: false
      });
    }
  }
});
